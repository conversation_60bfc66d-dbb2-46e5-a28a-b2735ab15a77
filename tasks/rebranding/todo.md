AromaCHAT Homepage Rebranding Plan - TODO
Based on my analysis of the current homepage feature and create-recipe functionality, I've created a comprehensive plan to rebrand the homepage from PassForge to AromaCHAT. Here's the detailed implementation plan:

🎯 Phase 1: Brand Migration (PassForge → AromaCHAT)
Files to Update:
src/components/icons/passforge-logo.tsx → Rename to aromachat-logo.tsx
src/components/icons/index.ts → Update export references
src/features/homepage/components/hero-header/hero-header.tsx → Replace PassForgeLogo import
src/features/homepage/README.md → Update all PassForge references to AromaCHAT
src/features/homepage/constants/hero-header-constants.ts → Update LOGO_TEXT from "Nexus" to "AromaCHAT"
Changes Required:
Replace all "PassForge" text with "AromaCHAT"
Update logo component and references
Update documentation and comments
🌿 Phase 2: Content Update (Essential Oil Recipe Focus)
Homepage Content Revamp:
Based on the create-recipe feature analysis, AromaCHAT is an AI-powered essential oil recipe creation application with these key features:

6-step guided wizard for personalized essential oil recommendations
AI-powered analysis based on health concerns, demographics, and symptoms
Real-time oil safety enrichment and therapeutic property analysis
Mobile-first responsive design
Content Updates Needed:
src/features/homepage/components/hero-content/hero-content.tsx:

Update hero title from "Deliver collaborative support" to "Create Personalized Essential Oil Recipes"
Update subtitle to reflect essential oil recipe creation
Replace "Works with Slack, Teams, Discord" with essential oil benefits
Update rotating words to focus on wellness, healing, aromatherapy
Update CTA from "See Nexus in action" to "Create Your Recipe"
src/features/homepage/constants/hero-header-constants.ts:

Update navigation items to reflect essential oil focus
Replace tech platform references with wellness/recipe creation terms
New Content Suggestions:
Hero Title: "Create Personalized Essential Oil Recipes with AI"
Hero Subtitle: "Discover the perfect essential oil blends for your wellness journey through our guided 6-step wizard"
Rotating Words: ["Wellness", "Healing", "Balance", "Relaxation", "Energy"]
CTA Button: "Start Creating Recipes"
Feature Highlights: "AI-Powered Analysis", "Safety First", "Personalized Recommendations"
🔐 Phase 3: Authentication Integration (Replace Newsletter)
Current State Analysis:
Homepage currently has email newsletter signup form
Clerk authentication is already integrated in header
Need to replace newsletter with prominent auth buttons
Implementation Plan:
src/features/homepage/components/hero-content/hero-content.tsx:

Replace newsletter signup form with Clerk SignIn/Register buttons
Add prominent "Sign In to Create Recipes" and "Register for Free" buttons
Maintain existing Clerk integration patterns
Add authentication call-to-action section:

Primary CTA: "Sign In to Start Creating"
Secondary CTA: "Register Free Account"
Remove email newsletter functionality entirely
Clerk Integration Points:
Use existing SignedIn/SignedOut components from @clerk/nextjs
Leverage existing UserButton integration
Redirect authenticated users to /create-recipe or /dashboard
📋 Implementation Order (DRY, KISS, YAGNI)
Step 1: Quick Wins (30 minutes)
1.
Update LOGO_TEXT constant to "AromaCHAT"
2.
Replace PassForgeLogo with AromaCHAT equivalent
3.
Update hero content text content
Step 2: Content Refinement (45 minutes)
1.
Revise hero title, subtitle, and rotating words
2.
Update navigation items to be recipe-focused
3.
Replace newsletter with auth buttons
Step 3: Polish & Test (15 minutes)
1.
Verify all branding is updated
2.
Test Clerk authentication flow
3.
Ensure responsive design maintained
🎯 Success Criteria
No remaining "PassForge" references
Homepage clearly communicates essential oil recipe creation purpose
Newsletter signup replaced with Clerk auth buttons
All existing functionality preserved
Mobile responsiveness maintained
Clean, focused user experience
📝 Files to Create/Modify
New Files:
src/components/icons/aromachat-logo.tsx (AromaCHAT logo component)
Modified Files:
src/features/homepage/components/hero-content/hero-content.tsx (main content)
src/features/homepage/components/hero-header/hero-header.tsx (header logo)
src/features/homepage/constants/hero-header-constants.ts (logo text)
src/components/icons/index.ts (export updates)
src/features/homepage/README.md (documentation update)
This plan follows DRY, KISS, and YAGNI principles by:

DRY: Reusing existing Clerk integration patterns
KISS: Simple text replacements and content updates
YAGNI: No new complex features, just essential rebranding