# Comprehensive Rebranding Plan: PassForge to AromaCHAT

This document outlines the detailed implementation plan for rebranding the application from "PassForge" to "AromaCHAT". This plan is based on a thorough analysis of the codebase and project documentation.

## Phase 1: Asset Updates

### 1.1. Create New Logo SVG

- **Task**: Create a new SVG logo for AromaCHAT. The existing PassForge logo is a placeholder and needs to be replaced with a design that reflects the new brand.
- **File to Create**: `src/components/icons/aromachat-logo.tsx`
- **Details**: A new, simple, and relevant logo will be created.

### 1.2. Update Logo Export

- **Task**: Update the `index.ts` file to export the new `aromachat-logo.tsx` component.
- **File to Update**: `src/components/icons/index.ts`

## Phase 2: Codebase Updates

### 2.1. Rename Logo Component File

- **Task**: Rename the `passforge-logo.tsx` file to `aromachat-logo.tsx`.
- **File to Rename**: `src/components/icons/passforge-logo.tsx` -> `src/components/icons/aromachat-logo.tsx`

### 2.2. Update Component Imports

- **Task**: Update the import statements in the codebase to reference the new `AromaCHATLogo` component.
- **Files to Update**:
    - `src/features/homepage/components/hero-header/hero-header.tsx`: Line 18

### 2.3. Replace All Instances of "PassForge"

- **Task**: Perform a global search and replace for all instances of "PassForge" (case-insensitive) with "AromaCHAT".
- **Files to Update**:
    - `src/features/homepage/README.md`: Line 6
    - `tasks/rebranding/prompt.md`: Lines 2, 4, 10, 22
    - `tasks/rebranding/todo.md`: Lines 2, 4, 6, 8, 12, 66, 84

## Phase 3: Content and Copy Updates

### 3.1. Update Homepage Content

- **Task**: Revise the homepage content to align with the AromaCHAT brand and its focus on essential oil recipe creation.
- **Files to Update**:
    - `src/features/homepage/components/hero-content/hero-content.tsx`
    - `src/features/homepage/constants/hero-header-constants.ts`
- **Details**:
    - Update the main headline, sub-headline, and rotating keywords.
    - Update the call-to-action buttons.
    - Update the navigation items.

## Phase 4: Authentication Integration

### 4.1. Replace Newsletter with Clerk Authentication

- **Task**: Remove the newsletter signup form and replace it with Clerk sign-in and sign-up buttons.
- **File to Update**: `src/features/homepage/components/hero-content/hero-content.tsx`
- **Details**:
    - Use the existing Clerk components (`SignedIn`, `SignedOut`, `UserButton`).
    - Ensure a smooth and consistent authentication flow.

## Phase 5: Verification

### 5.1. Run Linter

- **Task**: Run the linter to ensure that the code style remains consistent.
- **Command**: `npm run lint`

### 5.2. Manual Verification

- **Task**: Manually review the application to confirm that all instances of "PassForge" have been replaced and that the new branding is applied correctly.
