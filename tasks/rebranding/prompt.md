Overview:
rebrand homepage and all pages references from PassForge to AromaCHAT

- Replace all PassForge references with AromaCHAT branding
- Update homepage content to focus on essential oil recipe creation
- Replace newsletter signup with Clerk authentication buttons

I need help updating the homepage feature of my AromaCHAT application. This is a multi-part task that involves:

1. **Brand Migration**: Replace all references to "PassForge" with "AromaCHAT" throughout the homepage feature located at `/home/<USER>/aromachat-ui/src/features/homepage`. This app was built on top of a PassForge template and needs to be rebranded.

2. **Content Update**: Revamp the homepage text content (not placement) to reflect that AromaCHAT is an essential oil recipe creation application. The main feature is create-recipe (located at `/home/<USER>/aromachat-ui/src/features/create-recipe`), so the homepage should clearly communicate this purpose to first-time visitors. Study the whole recipe creation feature.

3. **Authentication Integration**: Replace the current newsletter signup feature on the homepage with Clerk authentication buttons (Sign In and Register). This will allow users to authenticate before accessing the recipe creation features.

Please:

- Remember that we follow a DRY, KISS and YAGNI concept.
- First analyze the current homepage feature structure and content
- Create a detailed plan in tasks/TODO.md before making changes
- Focus on making the homepage clearly communicate that this is a recipe creation app
- Ensure all PassForge branding is completely replaced with AromaCHAT
- Integrate Clerk authentication buttons in place of the newsletter feature
- Keep changes simple and targeted to minimize code impact