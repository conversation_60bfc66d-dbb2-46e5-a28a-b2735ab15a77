import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import '../styles/globals.css';
import { ThemeProvider } from '@/providers/theme-provider';
import QueryClientProvider from '@/providers/query-client-provider';
import { Toaster } from '@/components/ui/toaster';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { ClerkProvider } from '@clerk/nextjs';
import { getClerkAppearance } from '@/features/auth/config';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
});

export const metadata: Metadata = {
  title: 'AromaCHAT',
  description: 'AI-powered essential oil recipe creation platform',
};

/**
 * Static root layout - NO headers() usage for optimal performance
 * Locale-specific configuration is handled in [locale]/layout.tsx
 * This enables static generation for all routes including create-recipe
 */
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Static configuration - no dynamic locale detection here
  const clerkAppearance = getClerkAppearance();

  return (
    <ClerkProvider appearance={clerkAppearance}>
      <html suppressHydrationWarning>
        <body
          className={`${inter.variable} font-sans antialiased h-screen overflow-hidden`}
          suppressHydrationWarning
        >
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <QueryClientProvider>
              {children}
              <Toaster />
              {process.env.NODE_ENV === 'development' && (
                <ReactQueryDevtools initialIsOpen={false} />
              )}
            </QueryClientProvider>
          </ThemeProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
