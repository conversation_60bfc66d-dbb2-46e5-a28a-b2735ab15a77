
// src/app/[locale]/(dashboard)/dashboard/page.tsx
import { DashboardHomepageView } from '@/features/dashboard/dashboard-homepage';

interface Props {
  params: Promise<{ locale: string }>;
}

/**
 * Server Component for the main dashboard page (/[locale]/dashboard).
 * Updated to accept locale parameter for i18n support.
 * Middleware handles auth redirection.
 */
export default async function DashboardPage({ params }: Props): Promise<JSX.Element> {
  const { locale } = await params;

  return (
    <DashboardHomepageView />
  );
}
