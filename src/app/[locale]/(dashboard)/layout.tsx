import { QueryClient, HydrationBoundary, dehydrate } from '@tanstack/react-query';
import { getServerLogger } from '@/lib/logger';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { DashboardAppSidebar } from '@/features/dashboard/components/dashboard-app-sidebar';
import { DashboardHeader } from '@/features/dashboard/components';
import { LoadingProvider as DashboardLoadingProvider } from "@/features/ui/providers/loading-provider";

const logger = getServerLogger('DashboardLayout');
const shouldDebugServer = process.env.NODE_ENV === 'development' && 
                          process.env['NEXT_PUBLIC_DEBUG_SERVER'] === 'true';

/**
 * Static dashboard layout - The "Blueprint" approach
 *
 * SECURITY MODEL:
 * - Middleware acts as "Gatekeeper" using auth().protect()
 * - Layout is static "Blueprint" that can be pre-rendered
 * - Client components handle user-specific data via useUser()
 *
 * BENEFITS:
 * - Server-side security (middleware blocks unauthorized access)
 * - Static performance (layout pre-rendered at build time)
 * - Dynamic personalization (client components fill user data)
 * - Enables static generation for create-recipe routes
 *
 * This follows Clerk's recommended pattern: middleware for auth, static layouts for performance.
 * See: https://clerk.com/docs/guides/add-onboarding
 */
export default function DashboardLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  // STATIC LAYOUT: No async, no server-side auth checks
  // Middleware handles authentication as "gatekeeper"
  // This layout is just a static "blueprint" that can be pre-rendered

  // Create empty query client state for client-side hydration
  const queryClient = new QueryClient();
  const dehydratedState = dehydrate(queryClient);

  // Static generation - no dynamic server operations
  if (shouldDebugServer) {
    logger.info('DashboardLayout (Static): Pre-rendered blueprint served');
  }

  return (
    <DashboardLoadingProvider>
      <SidebarProvider initialVariant="inset" initialCollapsible="offcanvas">
        <DashboardAppSidebar />
        <SidebarInset className="bg-background flex flex-col h-screen">
          <DashboardHeader />
          <main className="flex-1 overflow-y-auto">
            <div className="p-4 md:p-6">
              <HydrationBoundary state={dehydratedState}>
                {children}
              </HydrationBoundary>
            </div>
          </main>
        </SidebarInset>
      </SidebarProvider>
    </DashboardLoadingProvider>
  );
}
