'use client';

import { useUser, useAuth } from '@clerk/nextjs';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { RefreshCw, User, AlertCircle, CheckCircle, XCircle } from 'lucide-react';

export default function ClerkDebugPage() {
  const { user, isLoaded } = useUser();
  const { getToken } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sessionToken, setSessionToken] = useState<any>(null);

  // Get session token for debugging
  const fetchSessionToken = async () => {
    try {
      const token = await getToken();
      if (token) {
        // Decode the JWT to inspect its claims (basic base64 decode for debugging)
        const payload = JSON.parse(atob(token.split('.')[1]));
        setSessionToken(payload);
      }
    } catch (error) {
      console.error('Error fetching session token:', error);
    }
  };

  useEffect(() => {
    if (user && isLoaded) {
      fetchSessionToken();
    }
  }, [user, isLoaded]);

  const forceTokenRefresh = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔄 Refreshing token and user data...');
      
      // Force token refresh (client-side)
      await getToken({ skipCache: true });
      
      // Reload user data
      await user?.reload();
      
      // Small delay to let the session update
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('✅ Token and user data refreshed');
      setError(null); // Clear any previous errors
      
      // Refresh session token for debugging
      await fetchSessionToken();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh token');
      console.error('Token refresh error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (!isLoaded) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="text-center py-8">
            <div className="animate-pulse">Loading Clerk SDK data...</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <XCircle className="w-5 h-5 text-red-500" />
              Not Authenticated
            </CardTitle>
            <CardDescription>
              You need to be signed in to view debug information.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  // Helper function to render onboarding status with clear visual indicators
  const renderOnboardingStatus = (value: any, source: string) => {
    const isComplete = Boolean(value);
    return (
      <div className="flex items-center gap-3 p-3 rounded-lg border">
        {isComplete ? (
          <div className="flex items-center gap-2">
            <CheckCircle className="w-6 h-6 text-green-500" />
            <div>
              <div className="font-semibold text-green-700">COMPLETED</div>
              <div className="text-sm text-green-600">{source}</div>
            </div>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <XCircle className="w-6 h-6 text-red-500" />
            <div>
              <div className="font-semibold text-red-700">NOT COMPLETED</div>
              <div className="text-sm text-red-600">{source}</div>
            </div>
          </div>
        )}
        <div className="ml-auto">
          <Badge variant={isComplete ? "default" : "destructive"} className="text-xs">
            {String(value)}
          </Badge>
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Clerk SDK Debug Dashboard</h1>
          <p className="text-muted-foreground">
            Inspect Clerk user data directly from the SDK (no API calls)
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button onClick={forceTokenRefresh} disabled={loading} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            {loading ? 'Refreshing...' : 'Refresh SDK Data'}
          </Button>
          
          <Button 
            onClick={() => window.location.href = `/${window.location.pathname.split('/')[1]}/dashboard`} 
            variant="secondary"
            className="text-sm"
          >
            Test Dashboard Access
          </Button>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800 flex items-center gap-2">
              <AlertCircle className="w-4 h-4" />
              Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* SDK Data Status */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800 flex items-center gap-2">
            <CheckCircle className="w-4 h-4" />
            SDK Data Successfully Loaded
          </CardTitle>
          <CardDescription className="text-blue-700">
            User data fetched directly from Clerk SDK hooks (useUser, useAuth)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>User ID:</strong> {user.id}
            </div>
            <div>
              <strong>Name:</strong> {user.firstName} {user.lastName}
            </div>
            <div>
              <strong>Email:</strong> {user.primaryEmailAddress?.emailAddress}
            </div>
            <div>
              <strong>Loaded At:</strong> {new Date().toLocaleString()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Onboarding Status Comparison */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-4 h-4" />
            Onboarding Status Comparison
          </CardTitle>
          <CardDescription>
            Clear visual comparison of onboarding completion status from different metadata sources
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Critical Alert for Incomplete Onboarding */}
          {!user.unsafeMetadata?.['onboardingComplete'] && (
            <div className="bg-red-50 border-2 border-red-200 rounded-lg p-4 mb-4">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="w-5 h-5 text-red-600" />
                <h4 className="font-bold text-red-800">CRITICAL: Onboarding Incomplete!</h4>
              </div>
              <div className="text-red-700 space-y-1 text-sm">
                <p>• User should be redirected to onboarding, not allowed here</p>
                <p>• If you can access this page, the middleware is NOT working correctly</p>
                <p>• Check Clerk Dashboard session token configuration</p>
                <p>• Clear browser cache and sign out/in again</p>
              </div>
            </div>
          )}
          
          <div className="space-y-3">
            {renderOnboardingStatus(
              user.publicMetadata?.['onboardingComplete'],
              'publicMetadata.onboardingComplete'
            )}
            
            {renderOnboardingStatus(
              user.unsafeMetadata?.['onboardingComplete'],
              'unsafeMetadata.onboardingComplete'
            )}
          </div>
          
          <Separator />
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-medium text-yellow-800 mb-2">Debug Analysis:</h4>
            <div className="space-y-1 text-sm text-yellow-700">
              <div>• According to Clerk's documentation: <strong>unsafeMetadata should be used for onboarding</strong></div>
              <div>• publicMetadata: Read-only from frontend, controlled by backend</div>
              <div>• unsafeMetadata: Read/write from frontend, perfect for user onboarding flows</div>
              <div>• Current middleware should check: <code>sessionClaims.metadata.unsafe.onboardingComplete</code></div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Session Token Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4" />
            Session Token Analysis (What Middleware Sees)
          </CardTitle>
          <CardDescription>
            This shows the actual JWT token claims that middleware uses for onboarding detection
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {sessionToken ? (
            <div className="space-y-4">
              {/* Critical Session Token Check */}
              {sessionToken.metadata && sessionToken.metadata.unsafe && sessionToken.metadata.unsafe.onboardingComplete !== undefined ? (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <h4 className="font-semibold text-green-800">Session Token Configured Correctly</h4>
                  </div>
                  <div className="text-green-700 text-sm">
                    <p>• Session token contains metadata.unsafe.onboardingComplete</p>
                    <p>• Middleware should be able to detect onboarding status</p>
                    <p>• Value: <strong>{JSON.stringify(sessionToken.metadata.unsafe.onboardingComplete)}</strong></p>
                  </div>
                </div>
              ) : (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <XCircle className="w-5 h-5 text-red-600" />
                    <h4 className="font-semibold text-red-800">Session Token Missing Metadata!</h4>
                  </div>
                  <div className="text-red-700 text-sm space-y-1">
                    <p>• Session token does NOT contain metadata.unsafe.onboardingComplete</p>
                    <p>• This is why middleware can't detect onboarding status</p>
                    <p>• Fix: Configure session token in Clerk Dashboard</p>
                    <p>• Then clear browser cache and sign out/in</p>
                  </div>
                </div>
              )}

              {/* Session Token Structure */}
              <div>
                <h4 className="font-medium mb-2">Full Session Token Claims:</h4>
                <div className="bg-gray-100 p-3 rounded text-xs">
                  <pre className="whitespace-pre-wrap overflow-auto max-h-60">
                    {JSON.stringify(sessionToken, null, 2)}
                  </pre>
                </div>
              </div>

              {/* Middleware Logic Simulation */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-semibold text-blue-800 mb-2">Middleware Logic Simulation:</h4>
                <div className="text-blue-700 text-sm space-y-1">
                  <p>• sessionClaims?.metadata?.unsafe?.onboardingComplete = <strong>{JSON.stringify(sessionToken.metadata?.unsafe?.onboardingComplete)}</strong></p>
                  <p>• !onboardingComplete = <strong>{!sessionToken.metadata?.unsafe?.onboardingComplete}</strong></p>
                  <p>• Middleware action: <strong>{!sessionToken.metadata?.unsafe?.onboardingComplete ? 'REDIRECT to onboarding' : 'ALLOW access'}</strong></p>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-muted-foreground">Loading session token...</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Complete Metadata Data */}
      <Card>
        <CardHeader>
          <CardTitle>Complete Metadata Data</CardTitle>
          <CardDescription>
            Full raw data returned from both metadata sources - exactly what the SDK provides
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Public Metadata */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="text-sm font-medium">
                  publicMetadata (Backend Controlled)
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {Object.keys(user.publicMetadata || {}).length} properties
                </Badge>
              </div>
              
              <div className="border rounded-lg">
                <div className="bg-blue-50 border-b p-3">
                  <div className="text-sm font-medium text-blue-800">Raw Data:</div>
                </div>
                <div className="p-4 bg-gray-50">
                  {Object.keys(user.publicMetadata || {}).length === 0 ? (
                    <div className="text-gray-500 italic text-sm">
                      No data - publicMetadata is empty
                    </div>
                  ) : (
                    <pre className="text-xs overflow-auto max-h-40 whitespace-pre-wrap">
                      {JSON.stringify(user.publicMetadata, null, 2)}
                    </pre>
                  )}
                </div>
              </div>
              
              {/* Key-Value breakdown for publicMetadata */}
              {user.publicMetadata && Object.keys(user.publicMetadata).length > 0 && (
                <div className="border rounded-lg">
                  <div className="bg-blue-50 border-b p-3">
                    <div className="text-sm font-medium text-blue-800">Key-Value Breakdown:</div>
                  </div>
                  <div className="p-3 space-y-2">
                    {Object.entries(user.publicMetadata).map(([key, value]) => (
                      <div key={key} className="flex items-start gap-3 p-2 bg-white rounded border">
                        <Badge variant="outline" className="text-xs shrink-0">{key}</Badge>
                        <div className="flex-1 min-w-0">
                          <div className="text-sm break-all">
                            <strong>Value:</strong> {JSON.stringify(value)}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            Type: {typeof value} | Length: {JSON.stringify(value).length}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Unsafe Metadata */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="text-sm font-medium">
                  unsafeMetadata (User/Frontend Controlled)
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {Object.keys(user.unsafeMetadata || {}).length} properties
                </Badge>
              </div>
              
              <div className="border rounded-lg">
                <div className="bg-green-50 border-b p-3">
                  <div className="text-sm font-medium text-green-800">Raw Data:</div>
                </div>
                <div className="p-4 bg-gray-50">
                  {Object.keys(user.unsafeMetadata || {}).length === 0 ? (
                    <div className="text-gray-500 italic text-sm">
                      No data - unsafeMetadata is empty
                    </div>
                  ) : (
                    <pre className="text-xs overflow-auto max-h-40 whitespace-pre-wrap">
                      {JSON.stringify(user.unsafeMetadata, null, 2)}
                    </pre>
                  )}
                </div>
              </div>
              
              {/* Key-Value breakdown for unsafeMetadata */}
              {user.unsafeMetadata && Object.keys(user.unsafeMetadata).length > 0 && (
                <div className="border rounded-lg">
                  <div className="bg-green-50 border-b p-3">
                    <div className="text-sm font-medium text-green-800">Key-Value Breakdown:</div>
                  </div>
                  <div className="p-3 space-y-2">
                    {Object.entries(user.unsafeMetadata).map(([key, value]) => (
                      <div key={key} className="flex items-start gap-3 p-2 bg-white rounded border">
                        <Badge variant="outline" className="text-xs shrink-0">{key}</Badge>
                        <div className="flex-1 min-w-0">
                          <div className="text-sm break-all">
                            <strong>Value:</strong> {JSON.stringify(value)}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            Type: {typeof value} | Length: {JSON.stringify(value).length}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          <Separator />
          
          {/* Data Comparison Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium mb-3">Metadata Comparison Summary</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>publicMetadata:</strong>
                <ul className="mt-1 space-y-1 text-gray-600">
                  <li>• Total properties: {Object.keys(user.publicMetadata || {}).length}</li>
                  <li>• Can only be modified by backend/server</li>
                  <li>• Read-only from frontend</li>
                  <li>• Secure for sensitive data</li>
                  {user.publicMetadata?.['onboardingComplete'] !== undefined && (
                    <li>• Contains onboardingComplete: {String(user.publicMetadata['onboardingComplete'])}</li>
                  )}
                </ul>
              </div>
              <div>
                <strong>unsafeMetadata:</strong>
                <ul className="mt-1 space-y-1 text-gray-600">
                  <li>• Total properties: {Object.keys(user.unsafeMetadata || {}).length}</li>
                  <li>• Can be modified by frontend/user</li>
                  <li>• Perfect for user preferences & onboarding</li>
                  <li>• Clerk's recommended approach for onboarding</li>
                  {user.unsafeMetadata?.['onboardingComplete'] !== undefined && (
                    <li>• Contains onboardingComplete: {String(user.unsafeMetadata['onboardingComplete'])}</li>
                  )}
                </ul>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-2">
            <h4 className="font-medium">Additional User Properties</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Created At:</strong> {user.createdAt ? new Date(user.createdAt).toLocaleString() : 'Unknown'}
              </div>
              <div>
                <strong>Updated At:</strong> {user.updatedAt ? new Date(user.updatedAt).toLocaleString() : 'Unknown'}
              </div>
              <div>
                <strong>Last Sign In:</strong> {user.lastSignInAt ? new Date(user.lastSignInAt).toLocaleString() : 'Never'}
              </div>
              <div>
                <strong>Email Verified:</strong> {user.primaryEmailAddress?.verification?.status || 'Unknown'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
