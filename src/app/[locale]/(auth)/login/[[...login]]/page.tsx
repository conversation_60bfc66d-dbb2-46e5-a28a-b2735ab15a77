import { SignIn, GoogleOneTap } from '@clerk/nextjs';

interface Props {
  params: Promise<{ locale: string }>;
}

/**
 * KISS approach: Use Clerk's built-in SignIn component with catch-all routing
 * This allows Clerk's internal routing to work properly (e.g., /login/verify-email)
 */
export default async function LoginPage({ params }: Props) {
  const { locale } = await params;

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <GoogleOneTap 
        signInForceRedirectUrl={`/${locale}/dashboard`}
        signUpForceRedirectUrl={`/${locale}/onboarding`}
      />
      <SignIn 
        routing="path"
        path={`/${locale}/login`}
        signUpUrl={`/${locale}/register`}
        fallbackRedirectUrl={`/${locale}/dashboard`}
        signUpFallbackRedirectUrl={`/${locale}/onboarding`}
        appearance={{
          elements: {
            formButtonPrimary: "bg-primary text-primary-foreground hover:bg-primary/90",
            card: "shadow-lg",
            socialButtonsBlockButton: "w-full mb-3 flex justify-center items-center gap-2 border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
            socialButtonsBlockButtonText: "text-gray-700",
            socialButtonsProviderIcon: "w-5 h-5",
            dividerLine: "bg-gray-300",
            dividerText: "text-gray-500 text-sm",
            formFieldInput: "w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary",
            footerActionLink: "text-primary hover:text-primary/80"
          },
          layout: {
            socialButtonsPlacement: "top",
            socialButtonsVariant: "blockButton"
          }
        }}
      />
    </div>
  );
}