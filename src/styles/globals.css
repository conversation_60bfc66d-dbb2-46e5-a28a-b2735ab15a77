@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hide scrollbars for terminal-like components */
@layer utilities {
  .scrollbar-hide {
    /* Firefox */
    scrollbar-width: none;
    /* Safari and Chrome */
    -ms-overflow-style: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* 3D Flip Card Utilities for Final Recipes */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* Timeline Navigation Utilities */
  .timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 1.5rem;
    bottom: 1.5rem;
    width: 3px;
    background-color: hsl(var(--border));
  }

  .timeline-item .dot {
    position: absolute;
    left: 1rem;
    top: 1.5rem;
    transform: translateX(-50%);
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 9999px;
    background-color: hsl(var(--background));
    border: 3px solid hsl(var(--border));
    transition: all 0.3s ease;
  }

  .timeline-item.active .dot {
    border-color: hsl(var(--primary));
    background-color: hsl(var(--primary));
  }

  /* Active timeline item highlight */
  .timeline-item.active-box > div[class*='bg-'] {
    box-shadow: 0 2px 8px 0 hsl(var(--primary) / 0.07), 0 0 0 2px hsl(var(--primary) / 0.22);
    background-color: hsl(var(--primary) / 0.05) !important;
    border: 1.5px solid hsl(var(--primary) / 0.3);
    transition: box-shadow 0.18s, border 0.18s, background 0.18s;
  }

  /* Droplet animation for recipe visualizer */
  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
  }

  .droplet {
    width: 8px;
    border-radius: 50%;
    animation: float 2s ease-in-out infinite;
  }
}

body {
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

@layer base {
:root {
  --background: 0 0% 99.2157%;
  --foreground: 0 0% 0%;
  --card: 0 0% 99.2157%;
  --card-foreground: 0 0% 0%;
  --popover: 0 0% 98.8235%;
  --popover-foreground: 0 0% 0%;
  --primary: 257.8652 80.1802% 56.4706%;
  --primary-foreground: 0 0% 100%;
  --secondary: 215.0000 20.6897% 88.6275%;
  --secondary-foreground: 0 0% 3.1373%;
  --muted: 0 0% 96.0784%;
  --muted-foreground: 0 0% 32.1569%;
  --accent: 221.7391 79.3103% 88.6275%;
  --accent-foreground: 216.5035 60.8511% 46.0784%;
  --destructive: 358.6567 59.8214% 56.0784%;
  --destructive-foreground: 0 0% 100%;
  --border: 240 13.0435% 86.4706%;
  --input: 0 0% 92.1569%;
  --ring: 0 0% 0%;
  --chart-1: 147.7778 42.8571% 50.5882%;
  --chart-2: 257.8652 80.1802% 56.4706%;
  --chart-3: 25.0549 78.4483% 54.5098%;
  --chart-4: 216.8627 61.4458% 51.1765%;
  --chart-5: 0 0% 45.4902%;
  --sidebar: 212 34.8837% 91.5686%;
  --sidebar-foreground: 0 0% 0%;
  --sidebar-primary: 0 0% 0%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 0 0% 92.1569%;
  --sidebar-accent-foreground: 0 0% 0%;
  --sidebar-border: 0 0% 92.1569%;
  --sidebar-ring: 0 0% 0%;
  --font-sans: Inter, ui-sans-serif, sans-serif, system-ui;
  --font-serif: Inter, ui-sans-serif, sans-serif, system-ui;
  --font-mono: Inter, ui-sans-serif, sans-serif, system-ui;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.05);
  --shadow-xs: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.05);
  --shadow-sm: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.10), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.10);
  --shadow: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.10), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.10);
  --shadow-md: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.10), 0px 2px 4px -1px hsl(0 0% 10.1961% / 0.10);
  --shadow-lg: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.10), 0px 4px 6px -1px hsl(0 0% 10.1961% / 0.10);
  --shadow-xl: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.10), 0px 8px 10px -1px hsl(0 0% 10.1961% / 0.10);
  --shadow-2xl: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: 220 5.6604% 10.3922%;
  --foreground: 0 0% 94.1176%;
  --card: 225.0000 5.8824% 13.3333%;
  --card-foreground: 0 0% 94.1176%;
  --popover: 225.0000 5.8824% 13.3333%;
  --popover-foreground: 0 0% 94.1176%;
  --primary: 257.5510 80.3279% 64.1176%;
  --primary-foreground: 0 0% 100%;
  --secondary: 222.8571 8.0460% 17.0588%;
  --secondary-foreground: 0 0% 94.1176%;
  --muted: 222.8571 8.0460% 17.0588%;
  --muted-foreground: 0 0% 62.7451%;
  --accent: 218.1818 26.1905% 16.4706%;
  --accent-foreground: 208.3200 79.6178% 69.2157%;
  --destructive: 0 72.9412% 66.6667%;
  --destructive-foreground: 0 0% 100%;
  --border: 228.0000 4.8544% 20.1961%;
  --input: 228.0000 4.8544% 20.1961%;
  --ring: 257.5510 80.3279% 64.1176%;
  --chart-1: 142.0313 55.1724% 54.5098%;
  --chart-2: 257.5510 80.3279% 64.1176%;
  --chart-3: 0 74.5763% 76.8627%;
  --chart-4: 217.8261 70.4082% 61.5686%;
  --chart-5: 0 0% 62.7451%;
  --sidebar: 240 1.9608% 10%;
  --sidebar-foreground: 0 0% 94.1176%;
  --sidebar-primary: 257.5510 80.3279% 64.1176%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 222.8571 8.0460% 17.0588%;
  --sidebar-accent-foreground: 257.5510 80.3279% 64.1176%;
  --sidebar-border: 228.0000 4.8544% 20.1961%;
  --sidebar-ring: 257.5510 80.3279% 64.1176%;
  --font-sans: Inter, ui-sans-serif, sans-serif, system-ui;
  --font-serif: Inter, ui-sans-serif, sans-serif, system-ui;
  --font-mono: Inter, ui-sans-serif, sans-serif, system-ui;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.05);
  --shadow-xs: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.05);
  --shadow-sm: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.10), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.10);
  --shadow: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.10), 0px 1px 2px -1px hsl(0 0% 10.1961% / 0.10);
  --shadow-md: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.10), 0px 2px 4px -1px hsl(0 0% 10.1961% / 0.10);
  --shadow-lg: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.10), 0px 4px 6px -1px hsl(0 0% 10.1961% / 0.10);
  --shadow-xl: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.10), 0px 8px 10px -1px hsl(0 0% 10.1961% / 0.10);
  --shadow-2xl: 0px 2px 5px 0px hsl(0 0% 10.1961% / 0.25);
}
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-sidebar text-foreground;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
  main {
    flex-grow: 1;
  }
}
  /* Final Recipe UI Styles - using theme variables only */
  
  /* Flip card animation styles */
  .flip-card {
    perspective: 1000px;
  }

  .flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.8s;
    transform-style: preserve-3d;
  }

  .flip-card.is-flipped .flip-card-inner {
    transform: rotateY(180deg);
  }

  .flip-card-front,
  .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 1rem;
    overflow: hidden;
  }

  .flip-card-back {
    transform: rotateY(180deg);
  }

  /* Utility classes for backface visibility */
  .backface-hidden {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* Droplet animation */
  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
  }

  .droplet {
    width: 8px;
    border-radius: 50%;
    animation: float 2s ease-in-out infinite;
  }

  /* Timeline styles */
  .timeline {
    position: relative;
  }

  .timeline-item .dot {
    position: absolute;
    left: 1rem;
    top: 1.5rem;
    transform: translateX(-50%);
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 9999px;
    background-color: hsl(var(--background));
    border: 3px solid hsl(var(--border));
    transition: all 0.3s ease;
  }

  .timeline-item.active .dot {
    border-color: hsl(var(--primary));
    background-color: hsl(var(--primary));
  }

  /* Active timeline highlighting */
  .timeline-item.active-box > div[class^='bg-'] {
    box-shadow: 0 2px 8px 0 hsl(var(--primary) / 0.07), 0 0 0 2px hsl(var(--primary) / 0.13);
    background-color: hsl(var(--primary) / 0.05) !important;
    border: 1.5px solid hsl(var(--primary) / 0.3);
    transition: box-shadow 0.18s, border 0.18s, background 0.18s;
  }

  /* Protocol card styles */
  .protocol-card {
    background: hsl(var(--card));
    border-radius: 20px;
    box-shadow: 0 8px 24px hsl(var(--foreground) / 0.12);
    overflow: hidden;
    max-width: 550px;
    width: 100%;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid hsl(var(--border));
  }

  .protocol-header {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
    color: hsl(var(--primary-foreground));
    padding: 24px 24px 60px;
    position: relative;
  }

  .time-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: hsl(var(--primary-foreground) / 0.2);
    color: hsl(var(--primary-foreground));
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 10;
  }

  .protocol-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 8px;
  }

  .protocol-subtitle {
    font-size: 1rem;
    opacity: 0.9;
  }

  .recipe-visual {
    background: hsl(var(--card));
    margin: -40px 24px 20px;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 16px hsl(var(--foreground) / 0.08);
    position: relative;
    z-index: 5;
  }

  .droplet-visualizer {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    gap: 4px;
    height: 40px;
    padding-bottom: 16px;
    margin-bottom: 8px;
  }

  .ingredient-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
  }

  .ingredient-badge {
    flex-shrink: 0;
    text-align: center;
    font-size: 0.8rem;
    font-weight: 600;
    background-color: hsl(var(--primary) / 0.1);
    color: hsl(var(--primary));
    border-radius: 8px;
    width: 60px;
    padding: 8px 4px;
  }

  .ingredient-details .name {
    font-weight: 600;
    color: hsl(var(--foreground));
  }

  .ingredient-details .botanical {
    font-size: 0.8rem;
    font-style: italic;
    color: hsl(var(--muted-foreground));
  }

  .collapsible-strip {
    list-style: none;
    cursor: pointer;
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
    color: hsl(var(--primary-foreground));
    padding: 16px 24px;
    transition: background 0.3s ease;
  }

  .collapsible-strip:hover {
    background: linear-gradient(135deg, hsl(var(--primary) / 0.8), hsl(var(--primary)));
  }

  .collapsible-strip .title {
    font-weight: 600;
  }

  .collapsible-strip .subtitle {
    font-size: 0.8rem;
    opacity: 0.8;
  }

  .collapsible-content {
    background-color: hsl(var(--muted) / 0.3);
    padding: 20px 24px;
  }

  .collapsible-strip::-webkit-details-marker {
    display: none;
  }

  .collapsible-strip .arrow {
    transition: transform 0.3s ease;
  }

  details[open] .collapsible-strip .arrow {
    transform: rotate(90deg);
  }