import React from 'react';

interface AromaCHATLogoProps extends React.SVGProps<SVGSVGElement> {
  // You can add any specific props for your logo here
}

export function AromaCHATLogo({ className, ...props }: AromaCHATLogoProps) {
  return (
    <svg
      className={className}
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="AromaCHAT Logo"
      {...props}
    >
      {/* Essential oil leaf with chat bubble design */}
      <path
        d="M12 24C12 18.477 16.477 14 22 14C22.552 14 23 13.552 23 13C23 12.448 22.552 12 22 12C15.373 12 10 17.373 10 24C10 30.627 15.373 36 22 36C22.552 36 23 35.552 23 35C23 34.448 22.552 34 22 34C16.477 34 12 29.523 12 24Z"
        fill="hsl(var(--primary))"
      />
      <path
        d="M26 20C26 17.791 27.791 16 30 16C32.209 16 34 17.791 34 20V28C34 30.209 32.209 32 30 32C27.791 32 26 30.209 26 28V20Z"
        fill="hsl(var(--accent))"
      />
      <path
        d="M16 18C16 16.895 16.895 16 18 16C19.105 16 20 16.895 20 18V22C20 23.105 19.105 24 18 24C16.895 24 16 23.105 16 22V18Z"
        fill="hsl(var(--primary))"
        opacity="0.7"
      />
      <circle
        cx="36"
        cy="12"
        r="6"
        fill="hsl(var(--accent))"
        opacity="0.8"
      />
      <circle
        cx="38"
        cy="10"
        r="2"
        fill="hsl(var(--background))"
      />
    </svg>
  );
}
