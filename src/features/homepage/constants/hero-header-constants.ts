
import type { NavItem } from '../types'; 

// Navigation items with i18n keys - labels will be translated at runtime
export const NAV_ITEMS_DESKTOP: NavItem[] = [
  { label: 'nav.product', href: '#features' },
  { label: 'nav.customers', href: '#how-it-works' },
  {
    label: 'nav.channels',
    href: '#safety', 
    children: [
      { label: 'nav.channels.safety', href: '#safety-guide' },
      { label: 'nav.channels.oilDatabase', href: '#oil-database' },
      { label: 'nav.channels.recipes', href: '#recipe-library' },
      { label: 'nav.channels.community', href: '#community' },
      { label: 'nav.channels.support', href: '#support' },
    ],
  },
  {
    label: 'nav.resources',
    href: '#resources', 
    children: [
      { label: 'nav.resources.oilGuide', href: '#oil-guide', icon: undefined },
      { label: 'nav.resources.safetyTips', href: '#safety-tips' },
      { label: 'nav.resources.helpCenter', href: '#help-center' },
      { label: 'nav.resources.apiReference', href: '#api-reference' },
    ],
  },
  { label: 'nav.docs', href: '#about' },
  { label: 'nav.pricing', href: '#pricing' },
];

export const NAV_ITEMS_MOBILE: NavItem[] = [
  { label: 'nav.product', href: '#features' },
  { label: 'nav.customers', href: '#how-it-works' },
  {
    label: 'nav.channels',
    href: '#safety', 
    children: [
      { label: 'nav.channels.safety', href: '#safety-guide' },
      { label: 'nav.channels.oilDatabase', href: '#oil-database' },
      { label: 'nav.channels.recipes', href: '#recipe-library' },
      { label: 'nav.channels.community', href: '#community' },
      { label: 'nav.channels.support', href: '#support' },
    ],
  },
  {
    label: 'nav.resources',
    href: '#resources', 
    children: [
      { label: 'nav.resources.oilGuide', href: '#oil-guide' },
      { label: 'nav.resources.safetyTips', href: '#safety-tips' },
      { label: 'nav.resources.helpCenter', href: '#help-center' },
    ],
  },
  { label: 'nav.docs', href: '#about' },
  { label: 'nav.pricing', href: '#pricing' },
];

export const LOGO_TEXT = "AromaCHAT";
