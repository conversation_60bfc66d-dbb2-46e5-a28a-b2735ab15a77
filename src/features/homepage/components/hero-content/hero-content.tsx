"use client";

import React from 'react';
import { motion, type Variants } from 'framer-motion';
import { SignedIn, SignedOut, SignInButton, SignUpButton } from '@clerk/nextjs';
import Link from 'next/link';
import { useServerI18n } from '../../layout/homepage-layout';
import ShinyText from '../shiny-text/shiny-text';
import RotatingText from '../rotating-text/rotating-text';

/**
 * Hero content component with optional server-side i18n integration
 * Uses server-provided translations when available, falls back to defaults
 */
export const HeroContent: React.FC = () => {
  // Try to get i18n context, but don't fail if it's not available
  let t: (key: string, fallback?: string) => string;

  try {
    const context = useServerI18n();
    t = context.t;
  } catch {
    // Fallback function when no i18n context is available
    t = (key: string, fallback?: string) => fallback || key;
  }
  
  const rotatingWords = [
    t('hero.rotatingWords.0', 'Support'),
    t('hero.rotatingWords.1', 'Experiences'),
    t('hero.rotatingWords.2', 'Relationships'),
    t('hero.rotatingWords.3', 'Help'),
    t('hero.rotatingWords.4', 'Service')
  ];
  
  const rotatingTextProps = {
    texts: rotatingWords,
    mainClassName: "text-primary mx-1", 
    staggerFrom: "last",
    initial: { y: "-100%", opacity: 0 }, 
    animate: { y: 0, opacity: 1 },
    exit: { y: "110%", opacity: 0 }, 
    staggerDuration: 0.01,
    transition: { type: "spring", damping: 18, stiffness: 250 },
    rotationInterval: 2200,
    splitBy: "characters", 
    auto: true,
    loop: true,
  } as const; 

  const contentDelay = 0.3;
  const itemDelayIncrement = 0.1;

  const bannerVariants: Variants = {
      hidden: { opacity: 0, y: -10 },
      visible: { opacity: 1, y: 0, transition: { duration: 0.4, delay: contentDelay } }
  };
 const headlineVariants: Variants = {
      hidden: { opacity: 0 },
      visible: { opacity: 1, transition: { duration: 0.5, delay: contentDelay + itemDelayIncrement } }
  };
  const subHeadlineVariants: Variants = {
      hidden: { opacity: 0, y: 10 },
      visible: { opacity: 1, y: 0, transition: { duration: 0.5, delay: contentDelay + itemDelayIncrement * 2 } }
  };
  const formVariants: Variants = {
      hidden: { opacity: 0, y: 10 },
      visible: { opacity: 1, y: 0, transition: { duration: 0.5, delay: contentDelay + itemDelayIncrement * 3 } }
  };
  const trialTextVariants: Variants = {
      hidden: { opacity: 0 },
      visible: { opacity: 1, transition: { duration: 0.5, delay: contentDelay + itemDelayIncrement * 4 } }
  };
  const worksWithVariants: Variants = {
      hidden: { opacity: 0 },
      visible: { opacity: 1, transition: { duration: 0.5, delay: contentDelay + itemDelayIncrement * 5 } }
  };
  const imageVariants: Variants = {
      hidden: { opacity: 0, scale: 0.95, y: 20 },
      visible: { opacity: 1, scale: 1, y: 0, transition: { duration: 0.6, delay: contentDelay + itemDelayIncrement * 6, ease: [0.16, 1, 0.3, 1] } }
  };

  return (
    <>
      <motion.div
          variants={bannerVariants}
          initial="hidden"
          animate="visible"
          className="mb-6"
      >
          <ShinyText text="" className="bg-muted border border-border text-primary px-4 py-1 rounded-full text-xs sm:text-sm font-medium cursor-pointer hover:border-primary/50 transition-colors">
            {t('hero.announcement', 'Announcing our $15M Series A')}
          </ShinyText>
      </motion.div>

      <motion.h1
          variants={headlineVariants}
          initial="hidden"
          animate="visible"
          className="text-4xl sm:text-5xl lg:text-[64px] font-semibold text-white leading-tight max-w-4xl mb-4"
      >
          {t('hero.title', 'Create Personalized')}<br />{' '}
          <span className="inline-block h-[1.2em] sm:h-[1.2em] lg:h-[1.2em] overflow-hidden align-bottom">
              <RotatingText {...rotatingTextProps} />
          </span>
      </motion.h1>

      <motion.p
          variants={subHeadlineVariants}
          initial="hidden"
          animate="visible"
          className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto mb-8"
      >
          {t('hero.subtitle', 'Support your customers on Slack, Microsoft Teams, Discord and many more – and move from answering tickets to building genuine relationships.')}
      </motion.p>

      <SignedOut>
        <motion.div
            variants={formVariants}
            initial="hidden"
            animate="visible"
            className="flex flex-col sm:flex-row items-center justify-center gap-3 w-full max-w-md mx-auto mb-3"
        >
            <SignUpButton mode="modal" fallbackRedirectUrl="/dashboard/create-recipe">
                <motion.button
                    className="w-full sm:w-auto bg-primary text-primary-foreground px-6 py-3 rounded-md text-sm font-semibold hover:bg-primary/90 transition-colors duration-200 whitespace-nowrap shadow-sm hover:shadow-md"
                    whileHover={{ scale: 1.03, y: -1 }}
                    whileTap={{ scale: 0.97 }}
                    transition={{ type: "spring", stiffness: 400, damping: 15 }}
                >
                    {t('hero.cta', 'Start Creating Recipes')}
                </motion.button>
            </SignUpButton>
            
            <SignInButton mode="modal" fallbackRedirectUrl="/dashboard/create-recipe">
                <motion.button
                    className="w-full sm:w-auto bg-background text-foreground border border-input px-6 py-3 rounded-md text-sm font-semibold hover:bg-accent transition-colors duration-200 whitespace-nowrap"
                    whileHover={{ scale: 1.03, y: -1 }}
                    whileTap={{ scale: 0.97 }}
                    transition={{ type: "spring", stiffness: 400, damping: 15 }}
                >
                    {t('nav.signIn', 'Sign In')}
                </motion.button>
            </SignInButton>
        </motion.div>
      </SignedOut>

      <SignedIn>
        <motion.div
            variants={formVariants}
            initial="hidden"
            animate="visible"
            className="flex flex-col items-center justify-center gap-3 w-full max-w-md mx-auto mb-3"
        >
            <Link href="/dashboard/create-recipe">
                <motion.button
                    className="w-full sm:w-auto bg-primary text-primary-foreground px-6 py-3 rounded-md text-sm font-semibold hover:bg-primary/90 transition-colors duration-200 whitespace-nowrap shadow-sm hover:shadow-md"
                    whileHover={{ scale: 1.03, y: -1 }}
                    whileTap={{ scale: 0.97 }}
                    transition={{ type: "spring", stiffness: 400, damping: 15 }}
                >
                    {t('hero.cta', 'Start Creating Recipes')}
                </motion.button>
            </Link>
        </motion.div>
      </SignedIn>

      <motion.p
          variants={trialTextVariants}
          initial="hidden"
          animate="visible"
          className="text-xs text-gray-500 mb-10"
      >
          {t('hero.trialText', 'Free 14 day trial')}
      </motion.p>

      <motion.div
          variants={worksWithVariants}
          initial="hidden"
          animate="visible"
          className="flex flex-col items-center justify-center space-y-2 mb-10"
      >
          <span className="text-xs uppercase text-gray-500 tracking-wider font-medium">{t('hero.worksWithTitle', 'Trusted for')}</span>
          <div className="flex flex-wrap items-center justify-center gap-x-4 gap-y-1 text-gray-400">
              <span className="flex items-center whitespace-nowrap">{t('hero.worksWithPlatforms.safety', '✓ Safety First')}</span>
              <span className="flex items-center whitespace-nowrap">{t('hero.worksWithPlatforms.aiPowered', '✓ AI-Powered')}</span>
              <span className="flex items-center whitespace-nowrap">{t('hero.worksWithPlatforms.personalized', '✓ Personalized')}</span>
              <span className="flex items-center whitespace-nowrap">{t('hero.worksWithPlatforms.scienceBased', '✓ Science-Based')}</span>
          </div>
      </motion.div>

      <motion.div
          variants={imageVariants}
          initial="hidden"
          animate="visible"
          className="w-full max-w-4xl mx-auto px-4 sm:px-0"
      >
          <img
              src="https://help.apple.com/assets/679AD2D1E874AD22770DE1E0/679AD2D56EA7B10C9E01288F/en_US/3d2b57c8027ae355aa44421899389008.png"
              alt={t('hero.imageAlt', 'Product screen preview showing collaborative features')}
              width={1024}
              height={640}
              className="w-full h-auto object-contain rounded-lg shadow-xl border border-gray-700/50"
              loading="lazy"
          />
      </motion.div>
    </>
  );
};
