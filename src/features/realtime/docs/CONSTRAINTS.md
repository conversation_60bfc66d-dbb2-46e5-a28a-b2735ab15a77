# Critical Constraints and Limitations

This document outlines **immutable constraints** that CANNOT be changed without breaking the realtime system.

## 🔒 Immutable Constraints

### 1. Single Channel Per Room Rule

**CONSTRAINT**: Only one `supabase.channel(roomName)` instance per room name per application.

**WHY**: Supabase channels can only be subscribed to once. Multiple subscriptions cause:
```
Error: tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance
```

**CANNOT CHANGE**:
- ❌ Multiple components using same room name
- ❌ Creating new channels for sending messages
- ❌ Re-subscribing to existing channels

**MUST MAINTAIN**:
- ✅ Single channel instance per room
- ✅ Store channel reference after subscription
- ✅ Reuse stored channel for all operations

### 2. Official Cleanup Pattern

**CONSTRAINT**: Must use `supabase.removeChannel(channel)` for cleanup.

**WHY**: Official Supabase documentation specifies this method for proper resource cleanup.

**CANNOT CHANGE**:
- ❌ Using only `channel.unsubscribe()`
- ❌ Skipping cleanup entirely
- ❌ Custom cleanup implementations

**MUST MAINTAIN**:
```typescript
return () => {
  supabase.removeChannel(realtimeChannel); // Required
  setChannel(null);
  setIsConnected(false);
};
```

### 3. Event Listener Setup Order

**CONSTRAINT**: Event listeners MUST be set up BEFORE calling `.subscribe()`.

**WHY**: Supabase requires listeners to be registered before subscription to capture all events.

**CANNOT CHANGE**:
```typescript
// ❌ WRONG ORDER: Subscribe first, then add listeners
channel.subscribe();
channel.on('broadcast', { event: 'test' }, handler); // Too late!

// ✅ CORRECT ORDER: Listeners first, then subscribe
channel
  .on('broadcast', { event: 'test' }, handler)
  .subscribe();
```

### 4. Channel Storage Pattern

**CONSTRAINT**: Channel reference MUST only be stored after `SUBSCRIBED` status.

**WHY**: Using channel before successful subscription causes send failures.

**CANNOT CHANGE**:
```typescript
// ❌ WRONG: Store immediately
const channel = supabase.channel(roomName);
setChannel(channel); // Too early!

// ✅ CORRECT: Store after subscription
.subscribe((status) => {
  if (status === 'SUBSCRIBED') {
    setChannel(realtimeChannel); // Only now!
  }
});
```

### 5. Design System Color Usage

**CONSTRAINT**: MUST use CSS variables, never hardcoded colors.

**WHY**: Hardcoded colors break theme switching and design consistency.

**CANNOT CHANGE**:
- ❌ `backgroundColor: '#ff0000'`
- ❌ `className="bg-white text-black"`
- ❌ `hsl(${Math.random() * 360}, 100%, 70%)`

**MUST MAINTAIN**:
- ✅ `backgroundColor: 'hsl(var(--chart-1))'`
- ✅ `className="bg-card text-foreground"`
- ✅ Design system color arrays

### 6. User Identification Pattern

**CONSTRAINT**: Email MUST be primary identifier, name is secondary.

**WHY**: Names can be empty or duplicate, emails are unique and required.

**CANNOT CHANGE**:
```typescript
// ❌ WRONG: Name as primary
user: { name: username }

// ✅ CORRECT: Email primary, name secondary
user: {
  email: user.email,           // Required, unique
  name: user.user_metadata?.['full_name'], // Optional
}
```

## ⚠️ Breaking Change Indicators

### Signs You've Violated Constraints

1. **Subscription Error**:
   ```
   tried to subscribe multiple times
   ```
   → Violated single channel constraint

2. **WebSocket Failures**:
   ```
   WebSocket connection failed
   ```
   → Violated cleanup or lifecycle constraints

3. **Theme Issues**:
   - Colors don't change with theme
   → Violated design system constraint

4. **Send Failures**:
   - Messages don't send
   → Violated channel storage constraint

5. **TypeScript Errors**:
   ```
   Cannot access before initialization
   ```
   → Violated variable initialization order

## 🛡️ Protection Mechanisms

### 1. Hook Dependencies

**CONSTRAINT**: Hook dependencies MUST include all variables used inside.

```typescript
// ✅ CORRECT: Complete dependencies
useEffect(() => {
  // Uses: user?.email, userId, supabase, roomName
}, [user?.email, userId, supabase, roomName]); // All included

// ❌ WRONG: Missing dependencies
useEffect(() => {
  // Uses: user?.email, userId, supabase, roomName
}, [roomName]); // Missing dependencies - will cause stale closures
```

### 2. State Initialization Order

**CONSTRAINT**: Variables MUST be defined before use in hooks.

```typescript
// ✅ CORRECT: Define first
const username = user?.email || '';
const { isConnected } = useHook({ username });

// ❌ WRONG: Use before definition
const { isConnected } = useHook({ username }); // Error!
const username = user?.email || '';
```

### 3. Payload Access Pattern

**CONSTRAINT**: Supabase payloads MUST use bracket notation.

```typescript
// ✅ CORRECT: Bracket notation
payload['payload'] as MessageType

// ❌ WRONG: Dot notation (TypeScript error)
payload.payload as MessageType
```

## 🔧 Maintenance Requirements

### 1. Regular Constraint Verification

**MUST CHECK**:
- [ ] No duplicate room names across components
- [ ] All cleanup uses `removeChannel()`
- [ ] All colors use design system variables
- [ ] All user identification includes email

### 2. Testing Requirements

**MUST TEST**:
- [ ] Multiple users in same room
- [ ] Component unmounting/remounting
- [ ] Theme switching
- [ ] Network disconnection/reconnection

### 3. Code Review Checklist

**MUST VERIFY**:
- [ ] Single channel pattern maintained
- [ ] Official cleanup pattern used
- [ ] Design system compliance
- [ ] Proper error handling
- [ ] TypeScript compliance

## 🚨 Emergency Procedures

### If Constraints Are Violated

1. **STOP**: Don't deploy broken code
2. **IDENTIFY**: Use error messages to find violation
3. **REVERT**: Return to last working state
4. **FIX**: Apply constraint-compliant solution
5. **TEST**: Verify fix with multiple users
6. **DOCUMENT**: Update this file if new constraints discovered

### Escalation Path

1. Check this document first
2. Check TROUBLESHOOTING.md
3. Review chat history for similar issues
4. Test with minimal reproduction case
5. Document new findings

---

**CRITICAL**: These constraints are based on official Supabase documentation and production failures. Violating them WILL cause system failures.

**Last Updated**: Based on production incidents and official documentation
**Status**: 🔒 IMMUTABLE - Do not modify without system-wide testing