# Realtime Architecture Overview

This document explains the architecture decisions and patterns used in our realtime system.

## 🏗️ Architecture Principles

### 1. DRY (Don't Repeat Yourself)
- **Reusable Components**: `RealtimeChat`, `RealtimeMouseTracking`, `Cursor`
- **Shared Hooks**: `useRealtimeChat`, `useRealtimeMouseTracking`, `useUnifiedRealtime`
- **Common Patterns**: Single channel, official cleanup, design system colors

### 2. YAGNI (You Aren't Gonna Need It)
- **No Over-Engineering**: Simple solutions for complex problems
- **Minimal Features**: Only implement what's actually needed
- **No Speculative Code**: Removed unused utilities and complex abstractions

### 3. KISS (Keep It Simple, Stupid)
- **Simple APIs**: Easy-to-use component interfaces
- **Clear Naming**: Descriptive function and variable names
- **Minimal Dependencies**: Use existing hooks and utilities

## 📊 Component Hierarchy

```
Application Level
├── Pages (Different Rooms)
│   ├── mouse-test (room: "mouse-test-room")
│   ├── chat-test (room: "chat-test-room")
│   └── supabase-realtime-test (room: "unified-test-room")
│
├── Feature Components
│   ├── RealtimeMouseTracking
│   │   ├── useRealtimeMouseTracking
│   │   └── Cursor (reusable)
│   │
│   ├── RealtimeChat
│   │   ├── useRealtimeChat
│   │   ├── ChatMessageItem (reusable)
│   │   └── useAutoScroll (external)
│   │
│   └── Unified (for same room)
│       ├── useUnifiedRealtime
│       ├── Cursor (reused)
│       └── ChatMessageItem (reused)
│
└── Core Utilities
    ├── useAutoScroll (external)
    ├── Design System Colors
    └── Official Supabase Patterns
```

## 🔄 Data Flow

### Single Feature Flow
```
User Action → Component → Hook → Supabase Channel → Other Users
    ↑                                                      ↓
User Sees Update ← Component ← Hook ← Supabase Channel ← Broadcast
```

### Unified Feature Flow (Same Room)
```
User Action → Unified Hook → Single Channel → Multiple Event Types
    ↑                                              ↓
Multiple Features ← State Updates ← Event Router ← Broadcast Events
```

## 🎯 Design Decisions

### 1. Single Channel Pattern
**Decision**: One Supabase channel per room name across entire application.

**Reasoning**:
- Supabase limitation: Cannot subscribe to same channel multiple times
- Performance: Single WebSocket connection per room
- Reliability: Prevents subscription conflicts

**Implementation**:
```typescript
// Global constraint: One channel per room
const channelMap = new Map<string, RealtimeChannel>();
```

### 2. Event-Based Architecture
**Decision**: Use different event types on same channel instead of separate channels.

**Reasoning**:
- Flexibility: Multiple features can share same room
- Performance: Single connection handles multiple data types
- Scalability: Easy to add new event types

**Implementation**:
```typescript
channel
  .on('broadcast', { event: 'chat_message' }, chatHandler)
  .on('broadcast', { event: 'cursor_move' }, cursorHandler)
  .on('broadcast', { event: 'click_event' }, clickHandler);
```

### 3. Design System Integration
**Decision**: All colors and styles use CSS variables from design system.

**Reasoning**:
- Consistency: Matches application theme
- Maintainability: Theme changes affect all components
- Accessibility: Supports dark/light mode switching

**Implementation**:
```typescript
const CHART_COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  // ...
];
```

### 4. Smart Auto-Scroll
**Decision**: Use advanced auto-scroll that respects user behavior.

**Reasoning**:
- UX: Don't interrupt users reading old messages
- Performance: Only scroll when user is near bottom
- Accessibility: Supports keyboard and touch navigation

**Implementation**:
```typescript
const { scrollRef } = useAutoScroll([messages.length], {
  threshold: 100,  // Only scroll if within 100px of bottom
  smooth: true,    // Smooth animation
});
```

## 🔧 Hook Architecture

### Individual Hooks
```typescript
// For single features in separate rooms
useRealtimeChat({ roomName, username })
useRealtimeMouseTracking({ roomName, options })
```

### Unified Hook
```typescript
// For multiple features in same room
useUnifiedRealtime({
  roomName,
  username,
  enableChat: boolean,
  enableMouse: boolean,
})
```

### Hook Responsibilities
- **Connection Management**: Handle Supabase channel lifecycle
- **State Management**: Manage messages, cursors, connection status
- **Event Handling**: Process incoming broadcasts
- **Error Handling**: Handle connection errors and timeouts
- **Cleanup**: Proper resource cleanup on unmount

## 🎨 Styling Architecture

### Design System Integration
```scss
// CSS Variables (from globals.css)
:root {
  --chart-1: 210 37.5000% 65.4902%;
  --chart-2: 12.9032 73.2283% 75.0980%;
  // ...
}

.dark {
  --chart-1: 210 37.5000% 65.4902%;
  // ... dark mode values
}
```

### Component Styling
```typescript
// Consistent class patterns
className="bg-card text-foreground border-border"
className="text-muted-foreground/70"
className="bg-green-50 text-green-700 dark:bg-green-950 dark:text-green-400"
```

## 🚀 Performance Optimizations

### 1. Throttling
```typescript
// Mouse events throttled to 50ms
const throttledSendCursor = useThrottleCallback(sendCursorPosition, 50);
```

### 2. Efficient Dependencies
```typescript
// Use length instead of full array for better performance
const { scrollRef } = useAutoScroll([messages.length], options);
```

### 3. Proper Cleanup
```typescript
// Prevent memory leaks
return () => {
  supabase.removeChannel(channel);
  setChannel(null);
  // Clear all state
};
```

### 4. State Optimization
```typescript
// Only update state for other users
if (cursorData.user.id !== userId) {
  setCursors(prev => ({ ...prev, [cursorData.user.id]: cursorData }));
}
```

## 🔒 Security Considerations

### 1. User Identification
- Primary: Email (unique, required)
- Secondary: Name (optional, display only)
- Never trust client-side user data

### 2. Data Validation
```typescript
// Always validate incoming data
const cursorData = payload['payload'] as CursorPosition;
if (cursorData?.user?.id && cursorData.user.id !== userId) {
  // Process data
}
```

### 3. Rate Limiting
- Throttling prevents spam
- Client-side throttling for UX
- Server-side rate limiting recommended

## 📈 Scalability Patterns

### 1. Room Isolation
- Each room is independent
- No cross-room data leakage
- Easy to scale horizontally

### 2. Event Type Extensibility
```typescript
// Easy to add new event types
.on('broadcast', { event: 'new_feature' }, handler)
```

### 3. Component Reusability
- Components work in any room
- Hooks are room-agnostic
- Easy to create new features

## 🧪 Testing Strategy

### 1. Unit Tests
- Hook behavior
- Component rendering
- Event handling

### 2. Integration Tests
- Multiple users in same room
- Component interactions
- Error scenarios

### 3. Performance Tests
- Memory leak detection
- Connection stability
- High-frequency events

---

**Design Philosophy**: Simple, reliable, and maintainable realtime features that scale with the application.

**Last Updated**: Based on production architecture and performance testing
**Status**: ✅ Production-proven architecture