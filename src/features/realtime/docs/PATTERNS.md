# Official Supabase Realtime Patterns

This document outlines the **official Supabase patterns** we discovered through the REF MCP server and production testing.

## 🎯 Core Pattern: Single Channel Instance

### The Golden Rule
**One `supabase.channel()` call per room name per application instance.**

```typescript
// ✅ CORRECT: Single channel instance
const channel = supabase.channel('room-name');
channel
  .on('broadcast', { event: 'event1' }, handler1)
  .on('broadcast', { event: 'event2' }, handler2)
  .subscribe();

// ❌ WRONG: Multiple channel instances
const channel1 = supabase.channel('room-name'); // First instance
const channel2 = supabase.channel('room-name'); // Second instance - CONFLICT!
```

## 📋 Official Implementation Pattern

### 1. Channel Creation and Setup
```typescript
useEffect(() => {
  if (!user?.email) return;

  // Create single channel instance
  const realtimeChannel = supabase.channel(roomName);

  // Set up ALL event listeners BEFORE subscribing
  realtimeChannel
    .on('broadcast', { event: 'event_type_1' }, (payload) => {
      // Handle event 1
    })
    .on('broadcast', { event: 'event_type_2' }, (payload) => {
      // Handle event 2
    })
    .subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        setIsConnected(true);
        setChannel(realtimeChannel); // Store ONLY after successful subscription
      } else if (status === 'CHANNEL_ERROR') {
        setIsConnected(false);
        console.error('Channel subscription error');
      } else if (status === 'TIMED_OUT') {
        setIsConnected(false);
        console.error('Channel subscription timed out');
      }
    });

  return () => {
    // Official cleanup method
    supabase.removeChannel(realtimeChannel);
    setChannel(null);
    setIsConnected(false);
  };
}, [user?.email, supabase, roomName]);
```

### 2. Message Sending Pattern
```typescript
const sendMessage = useCallback(
  async (data: any) => {
    if (!user?.email || !isConnected || !channel) return;

    // Use stored channel reference - NEVER create new channel
    await channel.send({
      type: 'broadcast',
      event: 'event_type',
      payload: data,
    });
  },
  [isConnected, channel, user?.email]
);
```

## 🔄 Lifecycle Management

### Status Handling
Always handle all three subscription statuses:
- `SUBSCRIBED` - Connection successful
- `CHANNEL_ERROR` - Connection failed
- `TIMED_OUT` - Connection timed out

### Cleanup Pattern
```typescript
return () => {
  // ✅ CORRECT: Official cleanup
  supabase.removeChannel(realtimeChannel);
  
  // ❌ WRONG: Just unsubscribe
  // realtimeChannel.unsubscribe(); // Incomplete cleanup
};
```

## 🎨 Design System Integration

### Color Generation
```typescript
// ✅ CORRECT: Use design system colors
const CHART_COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
];

const generateUserColor = (userId: string): string => {
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = ((hash << 5) - hash) + userId.charCodeAt(i);
  }
  return CHART_COLORS[Math.abs(hash) % CHART_COLORS.length];
};

// ❌ WRONG: Hardcoded colors
// const color = `hsl(${Math.random() * 360}, 100%, 70%)`;
```

### CSS Classes
```typescript
// ✅ CORRECT: Design system classes
className="bg-card text-foreground border-border"

// ❌ WRONG: Hardcoded classes
// className="bg-white text-black border-gray-200"
```

## 📱 Performance Patterns

### Throttling
```typescript
// ✅ CORRECT: Throttle high-frequency events
const useThrottleCallback = (callback, delay) => {
  // Implementation with proper cleanup
};

const throttledSendCursor = useThrottleCallback(sendCursorPosition, 50);
```

### Auto-Scroll
```typescript
// ✅ CORRECT: Use advanced auto-scroll
const { scrollRef } = useAutoScroll([messages.length], {
  threshold: 100,
  smooth: true,
  scrollDelay: 50,
  enabled: true,
});

// ❌ WRONG: Always scroll
// useEffect(() => {
//   scrollToBottom(); // Interrupts user reading
// }, [messages]);
```

## 🔐 Security Patterns

### User Identification
```typescript
// ✅ CORRECT: Use email as primary identifier
user: {
  email: user.email,
  name: user.user_metadata?.['full_name'],
}

// ❌ WRONG: Name-only identification
// user: { name: username }
```

### Data Validation
```typescript
// ✅ CORRECT: Validate payload structure
const cursorData = payload['payload'] as CursorPosition;
if (cursorData.user.id !== userId) {
  // Process data
}
```

## 📊 Event Types

### Standard Event Names
- `chat_message` - Chat messages
- `cursor_move` - Mouse cursor positions
- `click_event` - Click events
- `presence_sync` - User presence updates

### Payload Structure
```typescript
interface StandardPayload {
  id: string;
  user: {
    id: string;
    email: string;
    name?: string;
  };
  timestamp: number;
  // Event-specific data...
}
```

---

**Source**: Official Supabase documentation via REF MCP server
**Tested**: Production environment with multiple concurrent users
**Status**: ✅ Verified working patterns