# Realtime Troubleshooting Guide

This document covers all known issues and their solutions based on our production experience.

## 🚨 Critical Error: "tried to subscribe multiple times"

### Error Message
```
tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance
```

### Root Cause
Multiple components creating channels with the same room name.

### Immediate Fix
1. **Identify the conflict**:
   ```bash
   # Search for duplicate room names
   grep -r "roomName.*same-room-name" src/
   ```

2. **Choose solution**:
   - **Option A**: Use different room names
   - **Option B**: Use `useUnifiedRealtime` hook

### Solution A: Different Room Names
```tsx
// ✅ BEFORE (conflicting)
<RealtimeMouseTracking roomName="my-room" />
<RealtimeChat roomName="my-room" />  // CONFLICT!

// ✅ AFTER (separate rooms)
<RealtimeMouseTracking roomName="my-room-mouse" />
<RealtimeChat roomName="my-room-chat" />
```

### Solution B: Unified Hook
```tsx
// ✅ BEFORE (conflicting)
<RealtimeMouseTracking roomName="my-room" />
<RealtimeChat roomName="my-room" />  // CONFLICT!

// ✅ AFTER (unified)
const { cursors, messages, sendMessage, sendCursorPosition } = useUnifiedRealtime({
  roomName: 'my-room',
  username: '<EMAIL>',
  enableChat: true,
  enableMouse: true,
});
```

## 🔌 WebSocket Connection Issues

### Error Message
```
WebSocket connection failed: WebSocket is closed before the connection is established
```

### Common Causes
1. **Multiple subscription attempts**
2. **Improper cleanup**
3. **Network connectivity issues**

### Solutions

#### 1. Check Cleanup Pattern
```typescript
// ✅ CORRECT cleanup
useEffect(() => {
  const channel = supabase.channel(roomName);
  // ... setup
  
  return () => {
    supabase.removeChannel(channel); // Official method
    setChannel(null);
    setIsConnected(false);
  };
}, [roomName]);

// ❌ WRONG cleanup
return () => {
  channel.unsubscribe(); // Incomplete
};
```

#### 2. Verify Single Channel Pattern
```typescript
// ✅ CORRECT: One channel per room
const channelRegistry = new Map<string, RealtimeChannel>();

// ❌ WRONG: Multiple channels
const channel1 = supabase.channel('room');
const channel2 = supabase.channel('room'); // Duplicate!
```

## 🔄 Auto-Scroll Not Working

### Symptoms
- Chat messages don't auto-scroll
- Scroll position jumps unexpectedly
- Messages appear but user must manually scroll

### Solutions

#### 1. Check Hook Dependencies
```typescript
// ✅ CORRECT: Use message count for performance
const { scrollRef } = useAutoScroll([messages.length], options);

// ❌ WRONG: Use entire messages array
const { scrollRef } = useAutoScroll([messages], options); // Performance issue
```

#### 2. Verify Container Setup
```typescript
// ✅ CORRECT: Proper container ref
<div ref={scrollRef} className="overflow-y-auto">
  {messages.map(message => ...)}
</div>

// ❌ WRONG: Missing ref or overflow
<div className="overflow-hidden"> // Wrong overflow
  {messages.map(message => ...)}
</div>
```

#### 3. Check Auto-Scroll Configuration
```typescript
// ✅ CORRECT: Proper configuration
const { scrollRef } = useAutoScroll([messages.length], {
  threshold: 100,    // 100px from bottom
  smooth: true,      // Smooth animation
  scrollDelay: 50,   // DOM update delay
  enabled: true,     // Always enabled
});
```

## 🎨 Design System Issues

### Hardcoded Colors Not Updating

#### Problem
Colors don't change with theme switching.

#### Solution
```typescript
// ✅ CORRECT: Design system colors
className="bg-card text-foreground border-border"
style={{ backgroundColor: 'hsl(var(--chart-1))' }}

// ❌ WRONG: Hardcoded colors
className="bg-white text-black border-gray-200"
style={{ backgroundColor: '#ff0000' }}
```

### Connection Status Not Themed

#### Problem
Connection indicators use hardcoded green/red colors.

#### Solution
```typescript
// ✅ CORRECT: Themed status
<span className={`inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ${
  isConnected 
    ? 'bg-green-50 text-green-700 border border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800' 
    : 'bg-red-50 text-red-700 border border-red-200 dark:bg-red-950 dark:text-red-400 dark:border-red-800'
}`}>
```

## 🔧 Performance Issues

### High CPU Usage

#### Symptoms
- Browser becomes slow during mouse movement
- High CPU usage in DevTools

#### Solutions

1. **Check Throttling**:
   ```typescript
   // ✅ CORRECT: Throttled events
   const throttledSendCursor = useThrottleCallback(sendCursorPosition, 50);
   
   // ❌ WRONG: Unthrottled events
   onMouseMove={sendCursorPosition} // Fires too frequently
   ```

2. **Verify Event Cleanup**:
   ```typescript
   // ✅ CORRECT: Proper cleanup
   useEffect(() => {
     window.addEventListener('mousemove', handler);
     return () => window.removeEventListener('mousemove', handler);
   }, []);
   ```

### Memory Leaks

#### Symptoms
- Memory usage increases over time
- Browser becomes sluggish after extended use

#### Solutions

1. **Check Channel Cleanup**:
   ```typescript
   // ✅ CORRECT: Remove channels
   return () => {
     supabase.removeChannel(channel);
   };
   ```

2. **Verify State Cleanup**:
   ```typescript
   // ✅ CORRECT: Reset state
   return () => {
     setChannel(null);
     setIsConnected(false);
     setCursors({});
     setMessages([]);
   };
   ```

## 🐛 TypeScript Errors

### "Cannot access before initialization"

#### Error
```
ReferenceError: Cannot access 's' before initialization
```

#### Solution
```typescript
// ✅ CORRECT: Define before use
const username = user?.user_metadata?.['full_name'] || user?.email || '';
const { isConnected } = useRealtimeChat({ roomName, username });

// ❌ WRONG: Use before definition
const { isConnected } = useRealtimeChat({ roomName, username }); // Error!
const username = user?.email;
```

### Index Signature Access

#### Error
```
Property 'full_name' comes from an index signature, so it must be accessed with ['full_name']
```

#### Solution
```typescript
// ✅ CORRECT: Bracket notation
user.user_metadata?.['full_name']

// ❌ WRONG: Dot notation
user.user_metadata?.full_name // TypeScript error
```

## 🔍 Debugging Tools

### Enable Debug Logging
```typescript
// Add to component for debugging
useEffect(() => {
  if (process.env.NODE_ENV === 'development') {
    console.log('Realtime Debug:', {
      roomName,
      isConnected,
      messageCount: messages.length,
      cursorCount: Object.keys(cursors).length,
    });
  }
}, [roomName, isConnected, messages.length, cursors]);
```

### Check Channel Status
```typescript
// Monitor channel status
.subscribe((status) => {
  console.log(`Channel ${roomName} status:`, status);
  // Handle status...
});
```

### Network Tab Inspection
1. Open DevTools → Network tab
2. Filter by "WS" (WebSocket)
3. Look for Supabase realtime connections
4. Check for multiple connections to same room

---

**Emergency Contact**: Check chat history for detailed solutions
**Last Updated**: Based on production debugging sessions
**Status**: ✅ All issues have verified solutions