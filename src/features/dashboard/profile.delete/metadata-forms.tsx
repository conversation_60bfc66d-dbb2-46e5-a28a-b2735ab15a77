'use client'

import { useState } from 'react'
import { useUser } from '@clerk/nextjs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/use-toast'
import { Loader2, Save } from 'lucide-react'
import { setLanguageCookie } from '@/lib/actions/cookie.actions'

/**
 * Form for custom metadata (bio, display name, etc.)
 */
export function CustomMetadataForm() {
  const { user } = useUser()
  const { toast } = useToast()
  const [isUpdating, setIsUpdating] = useState(false)
  
  const [formData, setFormData] = useState({
    bio: (user?.unsafeMetadata?.['bio'] as string) || '',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setIsUpdating(true)
    try {
      console.log('🔍 Metadata: Starting client-side bio update...');

      // Update user using client-side method (reliable approach)
      await user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          bio: formData.bio,
          updatedAt: new Date().toISOString(),
        },
      });

      console.log('✅ Metadata: Bio update successful');

      toast({
        title: 'Preferences updated!',
        description: 'Your preferences have been saved successfully.',
      });
    } catch (error) {
      console.error('❌ Update error:', error)
      toast({
        title: 'Update failed',
        description: 'There was an error updating your preferences.',
        variant: 'destructive',
      })
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Personal Information</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              placeholder="Tell us about yourself..."
              value={formData.bio}
              onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
              className="min-h-[100px]"
            />
            <div className="text-sm text-gray-500">
              {formData.bio.length}/180 characters
            </div>
          </div>

          <Button type="submit" disabled={isUpdating} className="w-full">
            {isUpdating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Preferences
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}

/**
 * Form for language settings
 */
export function LanguageSettingsForm() {
  const { user } = useUser()
  const { toast } = useToast()
  const [isUpdating, setIsUpdating] = useState(false)
  
  const [language, setLanguage] = useState(
    (user?.unsafeMetadata?.['language'] as string) || 'en'
  )

  const handleLanguageChange = async (newLanguage: string) => {
    if (!user) return

    setIsUpdating(true)
    try {
      console.log('🔍 Metadata: Starting client-side language update...');

      // Update user using client-side method (reliable approach)
      await user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          language: newLanguage,
          updatedAt: new Date().toISOString(),
        },
      });

      console.log('✅ Metadata: Language update successful');

      // Set language cookie via server action
      const cookieResult = await setLanguageCookie(newLanguage);
      if (!cookieResult.success) {
        console.warn('Failed to set language cookie:', cookieResult.error);
        // Don't fail the entire operation since the main update succeeded
      }

      setLanguage(newLanguage)
      toast({
        title: 'Language updated!',
        description: `Language changed to ${getLanguageName(newLanguage)}.`,
      });
    } catch (error) {
      console.error('❌ Language update error:', error)
      toast({
        title: 'Update failed',
        description: 'There was an error updating your language preference.',
        variant: 'destructive',
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const getLanguageName = (code: string) => {
    const languages: Record<string, string> = {
      en: 'English',
      pt: 'Português',
      es: 'Español',
    }
    return languages[code] || code
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Language Preference</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Choose your preferred language</Label>
            <div className="grid grid-cols-1 gap-2">
              {[
                { code: 'en', name: 'English' },
                { code: 'pt', name: 'Português' },
                { code: 'es', name: 'Español' },
              ].map((lang) => (
                <Button
                  key={lang.code}
                  variant={language === lang.code ? 'default' : 'outline'}
                  onClick={() => handleLanguageChange(lang.code)}
                  disabled={isUpdating}
                  className="justify-start"
                >
                  {isUpdating && language === lang.code && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  {lang.name}
                </Button>
              ))}
            </div>
          </div>

          <div className="text-sm text-gray-600">
            Current selection: <strong>{getLanguageName(language)}</strong>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
