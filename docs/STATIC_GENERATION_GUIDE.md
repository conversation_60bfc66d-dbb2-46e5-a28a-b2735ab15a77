# Static Site Generation Guide for Next.js with Clerk Auth & i18n

## 🎯 Overview

This guide documents the critical patterns and pitfalls for maintaining **Static Site Generation (SSG)** in a Next.js application with Clerk authentication and internationalization. Our implementation successfully achieves static generation for AI-generated content routes while maintaining server-side security and SEO optimization.

## 🏗️ Architecture Overview: "Gatekeeper vs Blueprint" Pattern

### The Core Concept

We implement a **separation of concerns** between security and rendering:

- **Middleware (The Gatekeeper)**: Handles all dynamic logic (auth checks, redirects, locale detection)
- **Layouts/Pages (The Blueprint)**: Remain static and can be pre-rendered at build time

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Middleware    │    │   Static Layout │    │  Client Components│
│  (Gatekeeper)   │───▶│   (Blueprint)   │───▶│ (Personalization) │
│                 │    │                 │    │                   │
│ • Auth checks   │    │ • No headers()  │    │ • useUser()       │
│ • Redirects     │    │ • No async auth │    │ • Dynamic data    │
│ • Locale detect │    │ • Pre-rendered  │    │ • User-specific   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Benefits Achieved

✅ **Server-Side Security**: Middleware blocks unauthorized access before layouts render  
✅ **Static Performance**: Layouts pre-rendered at build time, served from CDN  
✅ **Perfect SEO**: Static HTML with proper locale handling and metadata  
✅ **Dynamic Personalization**: Client components handle user-specific data  

## ⚠️ Critical Rules for Static Generation

### 🚫 NEVER Do These in Layouts/Pages

1. **Never use `headers()` function**
   ```tsx
   // ❌ BREAKS STATIC GENERATION
   import { headers } from 'next/headers';
   
   export default async function Layout() {
     const headersList = await headers(); // Forces dynamic rendering
     const locale = headersList.get('x-locale');
   }
   ```

2. **Never call server-side auth in layouts**
   ```tsx
   // ❌ BREAKS STATIC GENERATION
   import { currentUser } from '@clerk/nextjs/server';
   
   export default async function Layout() {
     const user = await currentUser(); // Uses headers() internally
   }
   ```

3. **Never nest `<html>` or `<body>` tags**
   ```tsx
   // ❌ CAUSES HYDRATION ERRORS
   export default function LocaleLayout({ children }) {
     return (
       <html lang="es">  {/* Already exists in root layout */}
         <body>         {/* Causes nested body tags */}
           {children}
         </body>
       </html>
     );
   }
   ```

### ✅ DO These Instead

1. **Use URL params for locale detection**
   ```tsx
   // ✅ STATIC - Gets locale from URL params
   export default async function LocaleLayout({ params }) {
     const { locale } = await params; // Static from URL
     const clerkConfig = getClerkLocalization(locale);
   }
   ```

2. **Use client components for user data**
   ```tsx
   // ✅ STATIC LAYOUT + CLIENT PERSONALIZATION
   'use client';
   import { useUser } from '@clerk/nextjs';
   
   export function UserHeader() {
     const { user } = useUser(); // Client-side only
     return <span>Welcome, {user?.firstName}!</span>;
   }
   ```

3. **Use `generateMetadata` for SEO**
   ```tsx
   // ✅ SERVER-SIDE SEO WITHOUT BREAKING STATIC GENERATION
   export async function generateMetadata({ params }) {
     const { locale } = await params;
     return {
       alternates: {
         languages: {
           'en': '/en',
           'pt': '/pt',
           'es': '/es',
         }
       }
     };
   }
   ```

## 🐛 Common Pitfalls & Solutions

### Pitfall 1: API Routes Getting Locale Prefixes

**Problem**: Middleware redirects `/api/ai/streaming` to `/es/api/ai/streaming`, breaking API endpoints.

**Solution**: Exclude API routes from locale redirection:
```tsx
// ✅ CORRECT - Exclude API routes
if (pathnameIsMissingLocale && !pathname.startsWith('/api/')) {
  const locale = detectLocale(req);
  return NextResponse.redirect(new URL(`/${locale}/${pathname}`, req.url));
}
```

### Pitfall 2: Incorrect `auth().protect()` Usage

**Problem**: Calling `auth().protect()` inside middleware handler causes runtime errors.

**Solution**: Use `redirectToSignIn` pattern:
```tsx
// ✅ CORRECT MIDDLEWARE AUTH PATTERN
export default clerkMiddleware(async (auth, req) => {
  const { userId, redirectToSignIn } = await auth();
  
  if (!isPublicRoute(req) && !userId) {
    return redirectToSignIn({ returnBackUrl: req.url });
  }
});
```

### Pitfall 3: Setting Headers in Middleware

**Problem**: Setting `x-locale` header tempts future developers to use `headers()` in layouts.

**Anti-Pattern**:
```tsx
// ❌ DANGEROUS - Tempts headers() usage in layouts
const response = NextResponse.next();
response.headers.set('x-locale', locale); // Don't do this!
return response;
```

**Solution**: Keep locale in URL params only - it's already perfectly encoded there.

### Pitfall 4: Dynamic Server Usage Errors

**Error**: `Route couldn't be rendered statically because it used 'headers'`

**Root Causes**:
- Any `headers()` call in server components
- `currentUser()` from Clerk (uses headers internally)  
- `cookies()` calls in server components
- Server-side auth utilities that call the above

**Solution**: Move all dynamic logic to middleware, keep layouts static.

## 🛡️ Middleware Best Practices

### Correct Clerk Middleware Pattern

```tsx
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

// Define public routes - everything else is protected
const isPublicRoute = createRouteMatcher([
  '/',
  '/(en|pt|es)',
  '/(en|pt|es)/login(.*)',
  '/(en|pt|es)/sign-in(.*)',
  '/api/webhooks/(.*)', // Public API routes
]);

export default clerkMiddleware(async (auth, req) => {
  const { pathname } = req.nextUrl;
  
  // 1. Handle locale redirection FIRST
  if (pathname === '/') {
    const locale = detectLocale(req);
    return NextResponse.redirect(new URL(`/${locale}`, req.url));
  }
  
  // 2. Protect non-public routes
  const { userId, redirectToSignIn } = await auth();
  if (!isPublicRoute(req) && !userId) {
    return redirectToSignIn({ returnBackUrl: req.url });
  }
  
  // 3. Handle onboarding logic
  // ... onboarding checks
  
  return NextResponse.next();
});
```

### API Route Protection

```tsx
// ✅ CORRECT - Exclude API routes from locale handling
const isApiRoute = pathname.startsWith('/api/');
const pathnameIsMissingLocale = i18n.locales.every(
  (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
);

if (pathnameIsMissingLocale && !isApiRoute) {
  // Only redirect non-API routes
  const locale = detectLocale(req);
  return NextResponse.redirect(new URL(`/${locale}/${pathname}`, req.url));
}
```

## 🔧 Troubleshooting Guide

### Identifying Static Generation Issues

1. **Check Build Output**:
   ```bash
   npm run build
   ```
   Look for:
   - `●` = SSG (Static Site Generation) ✅
   - `ƒ` = Dynamic (Server-rendered) ❌

2. **Common Error Messages**:
   - `"Dynamic server usage: Route ... couldn't be rendered statically because it used 'headers'"`
   - `"TypeError: auth(...).protect is not a function"`
   - `"Hydration failed because the server rendered HTML didn't match the client"`

### Debugging Steps

1. **Find the `headers()` call**:
   ```bash
   # Search for headers() usage
   grep -r "headers()" src/
   grep -r "currentUser()" src/
   grep -r "cookies()" src/
   ```

2. **Check middleware configuration**:
   - Ensure API routes are excluded from locale redirection
   - Verify correct `clerkMiddleware` usage
   - Check public route patterns

3. **Validate layout structure**:
   - No nested `<html>` or `<body>` tags
   - No server-side auth calls in layouts
   - Use `generateMetadata` for SEO instead of manual head tags

### Quick Fixes

| Issue | Quick Fix |
|-------|-----------|
| Dynamic server usage | Move `headers()` calls to middleware |
| Hydration mismatch | Remove nested HTML tags from locale layout |
| API routes broken | Add `!pathname.startsWith('/api/')` to locale logic |
| Auth errors in middleware | Use `redirectToSignIn` instead of `auth().protect()` |

## 📊 Success Metrics

Our implementation achieves:

- ✅ **Static Generation**: All create-recipe routes are pre-rendered (`●` in build output)
- ✅ **Server-Side Security**: Middleware protects routes before rendering
- ✅ **Perfect SEO**: Proper `hreflang` tags and locale-specific metadata
- ✅ **Performance**: Static assets served from CDN
- ✅ **User Experience**: Client-side personalization after static shell loads

## 🚀 Future Development Guidelines

When adding new features:

1. **Always check build output** after changes
2. **Keep layouts static** - move dynamic logic to middleware or client components
3. **Test with multiple locales** to ensure locale handling works
4. **Verify API routes** aren't affected by locale redirection
5. **Use the "Gatekeeper vs Blueprint" pattern** for new protected routes

Remember: **Static generation is fragile** - one `headers()` call can break the entire build. When in doubt, move logic to middleware or client components.

## 📁 File Structure Reference

### Key Files in Our Implementation

```
src/
├── middleware.ts                 # Gatekeeper - handles all dynamic logic
├── app/
│   ├── layout.tsx               # Root layout - STATIC (no headers())
│   └── [locale]/
│       ├── layout.tsx           # Locale layout - STATIC (uses params)
│       └── (dashboard)/
│           ├── layout.tsx       # Dashboard layout - STATIC (no auth calls)
│           └── create-recipe/
│               └── [step]/
│                   └── page.tsx # AI content - STATIC ✅
├── features/auth/
│   ├── config/
│   │   └── clerk-localization.ts # Static Clerk config
│   └── components/
│       └── user-button/         # Client components for user data
└── docs/
    └── STATIC_GENERATION_GUIDE.md # This guide
```

## 🔍 Code Examples

### ✅ Correct Static Layout Pattern

```tsx
// src/app/[locale]/(dashboard)/layout.tsx
export default function DashboardLayout({ children, params }) {
  // STATIC: No async, no headers(), no auth calls
  const queryClient = new QueryClient();
  const dehydratedState = dehydrate(queryClient);

  return (
    <SidebarProvider>
      <HydrationBoundary state={dehydratedState}>
        <DashboardAppSidebar />
        <SidebarInset>
          <DashboardHeader /> {/* Client component handles user data */}
          <main>{children}</main>
        </SidebarInset>
      </HydrationBoundary>
    </SidebarProvider>
  );
}
```

### ✅ Correct Client Component for User Data

```tsx
// src/features/dashboard/components/dashboard-header.tsx
'use client';
import { useUser } from '@clerk/nextjs';

export function DashboardHeader() {
  const { user, isLoaded } = useUser();

  if (!isLoaded) {
    return <HeaderSkeleton />; // Loading state
  }

  return (
    <header>
      <h1>Welcome, {user?.firstName}!</h1>
      <UserButton /> {/* Clerk's client component */}
    </header>
  );
}
```

### ✅ Correct Middleware Implementation

```tsx
// src/middleware.ts - Complete working example
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import { detectLocale } from '@/lib/i18n';

const isPublicRoute = createRouteMatcher([
  '/', '/(en|pt|es)', '/(en|pt|es)/login(.*)', '/api/webhooks/(.*)'
]);

export default clerkMiddleware(async (auth, req) => {
  const { pathname } = req.nextUrl;

  // 1. Root redirect
  if (pathname === '/') {
    const locale = detectLocale(req);
    return NextResponse.redirect(new URL(`/${locale}`, req.url));
  }

  // 2. Locale handling (exclude API routes)
  const isApiRoute = pathname.startsWith('/api/');
  const pathnameIsMissingLocale = i18n.locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );

  if (pathnameIsMissingLocale && !isApiRoute) {
    const locale = detectLocale(req);
    const sanitizedPathname = pathname.startsWith("/") ? pathname.substring(1) : pathname;
    return NextResponse.redirect(new URL(`/${locale}/${sanitizedPathname}`, req.url));
  }

  // 3. Auth protection
  const { userId, redirectToSignIn, sessionClaims } = await auth();
  if (!isPublicRoute(req) && !userId) {
    return redirectToSignIn({ returnBackUrl: req.url });
  }

  // 4. Onboarding logic
  if (userId && !(sessionClaims as any)?.metadata?.unsafe?.onboardingComplete) {
    if (!pathname.includes('/onboarding')) {
      const locale = pathname.split('/')[1] || 'en';
      return NextResponse.redirect(new URL(`/${locale}/onboarding`, req.url));
    }
  }

  return NextResponse.next();
});

export const config = {
  matcher: ['/((?!.*\\..*|_next).*)', '/', '/(api|trpc)(.*)'],
};
```

## 🎯 Testing Your Implementation

### Build Test Checklist

```bash
# 1. Clean build
npm run build

# 2. Check for static generation symbols
# Look for ● (SSG) not ƒ (Dynamic) in build output

# 3. Test locale redirection
curl -I http://localhost:3000/
# Should redirect to /en (or user's preferred locale)

# 4. Test API routes
curl http://localhost:3000/api/health
# Should NOT redirect to /en/api/health

# 5. Test protected routes
curl http://localhost:3000/en/dashboard
# Should redirect to sign-in if not authenticated
```

### Common Build Output Patterns

```
✅ GOOD - Static Generation Working:
Route (app)                              Size     First Load JS
┌ ● /[locale]/dashboard/create-recipe/[step]  71 kB    351 kB
├   ├ /en/dashboard/create-recipe/health-concern
├   ├ /pt/dashboard/create-recipe/health-concern
└   └ /es/dashboard/create-recipe/health-concern

❌ BAD - Dynamic Rendering:
Route (app)                              Size     First Load JS
┌ ƒ /[locale]/dashboard/create-recipe/[step]  71 kB    351 kB
└   └ (Dynamic route - rendered on demand)
```

## 📚 Additional Resources

- [Next.js Static Generation Docs](https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic)
- [Clerk Middleware Guide](https://clerk.com/docs/references/nextjs/clerk-middleware)
- [Next.js i18n Routing](https://nextjs.org/docs/app/building-your-application/routing/internationalization)
- [React Hydration Errors](https://nextjs.org/docs/messages/react-hydration-error)

---

**⚡ Key Takeaway**: The "Gatekeeper vs Blueprint" pattern enables you to have both server-side security AND static generation. Middleware handles all the dynamic stuff, layouts stay static and fast.
