# Clerk Appearance Customization Guide

## Overview

This guide explains how to properly customize <PERSON>'s built-in authentication components (`SignIn`, `SignUp`, `UserProfile`, etc.) while maintaining consistency with our design system.

## Core Principles

### ❌ NO HARDCODED VALUES ALLOWED
- Never use hardcoded colors, shadows, or dimensions
- Always use CSS variables from our design system
- Follow the DRY principle - reuse existing design tokens

### ✅ Use Design System Variables
All styling must use variables defined in `src/styles/globals.css`:

```css
/* Colors */
--primary, --primary-foreground
--background, --foreground
--card, --card-foreground
--muted, --muted-foreground
--border, --input, --ring
--destructive, --accent

/* Typography */
--font-sans, --font-serif, --font-mono

/* Layout */
--radius

/* Shadows */
--shadow-sm, --shadow-md, --shadow-lg, --shadow-xl
```

## Configuration Location

Clerk appearance is configured in:
```
src/features/auth/config/clerk-appearance.ts
```

## Structure

The configuration has two main sections:

### 1. Variables Section
Maps Clerk's internal variables to our design system:

```typescript
variables: {
  // Colors - always use CSS variables
  colorPrimary: 'hsl(var(--primary))',
  colorBackground: 'hsl(var(--background))',
  colorForeground: 'hsl(var(--foreground))',
  
  // Typography - use design system fonts
  fontFamily: 'var(--font-sans)',
  
  // Layout - use design system values
  borderRadius: 'var(--radius)',
}
```

### 2. Elements Section
Styles specific Clerk components:

```typescript
elements: {
  // Target specific Clerk elements
  socialButtonsBlockButton: {
    backgroundColor: 'hsl(var(--card))',
    borderColor: 'hsl(var(--border))',
    boxShadow: 'var(--shadow-sm)',
    // ... more styles
  }
}
```

## Common Clerk Element Selectors

### Authentication Forms
- `card` - Main container
- `formButtonPrimary` - Primary action buttons
- `formButtonSecondary` - Secondary action buttons
- `formFieldInput` - Input fields

### OAuth/Social Buttons
- `socialButtonsBlockButton` - OAuth provider buttons
- `socialButtonsBlockButtonText` - Text inside OAuth buttons
- `socialButtonsBlockButtonArrow` - Arrow icons (usually hidden)

### Navigation & Layout
- `navbar` - Navigation bar
- `navbarButton` - Navigation buttons
- `headerTitle` - Page titles
- `headerSubtitle` - Page subtitles

### Dividers & Separators
- `dividerLine` - Separator lines
- `dividerText` - "or" text between sections

### Profile Components
- `profileSectionPrimaryButton` - Profile action buttons
- `modalBackdrop` - Modal overlays (usually hidden)
- `modalCloseButton` - Close buttons (usually hidden)

## Best Practices

### 1. Color Usage
```typescript
// ✅ CORRECT - Use design system variables
backgroundColor: 'hsl(var(--card))',
color: 'hsl(var(--foreground))',

// ❌ WRONG - Hardcoded values
backgroundColor: '#ffffff',
color: '#000000',
```

### 2. Shadow Usage
```typescript
// ✅ CORRECT - Use design system shadows
boxShadow: 'var(--shadow-sm)',

// ❌ WRONG - Hardcoded shadow
boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
```

### 3. Typography
```typescript
// ✅ CORRECT - Use design system fonts
fontFamily: 'var(--font-sans)',

// ❌ WRONG - Hardcoded font
fontFamily: 'Inter, sans-serif',
```

### 4. Hover States
```typescript
// ✅ CORRECT - Use design system variables
'&:hover': {
  backgroundColor: 'hsl(var(--accent))',
  boxShadow: 'var(--shadow-md)'
}

// ❌ WRONG - Hardcoded hover states
'&:hover': {
  backgroundColor: '#f5f5f5',
  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
}
```

## Testing Your Changes

### 1. Light/Dark Theme Compatibility
Ensure your changes work in both themes:
- Test with light theme (default)
- Test with dark theme
- Variables should automatically adapt

### 2. Component Testing
Test all authentication flows:
- Sign in page (`/[locale]/login`)
- Sign up page (`/[locale]/register`)
- User profile modal
- OAuth button functionality

### 3. Responsive Design
Verify styling works across screen sizes:
- Mobile (320px+)
- Tablet (768px+)
- Desktop (1024px+)

## Common Issues & Solutions

### OAuth Buttons Not Visible
**Problem**: Buttons blend into background
**Solution**: Ensure proper contrast using design system variables:

```typescript
socialButtonsBlockButton: {
  backgroundColor: 'hsl(var(--card))',
  borderColor: 'hsl(var(--border))',
  color: 'hsl(var(--foreground))',
  border: '1px solid hsl(var(--border))',
  boxShadow: 'var(--shadow-sm)',
}
```

### Inconsistent Styling
**Problem**: Some elements don't match design system
**Solution**: Check if you're using hardcoded values instead of CSS variables

### Theme Switching Issues
**Problem**: Components don't adapt to theme changes
**Solution**: Ensure all colors use `hsl(var(--variable-name))` format

## Debugging Tips

### 1. Inspect Clerk Elements
Use browser dev tools to identify Clerk's internal class names:
1. Right-click on the element
2. Inspect element
3. Look for Clerk-specific classes (usually prefixed with `cl-`)

### 2. CSS Variable Verification
Check if variables are properly defined:
```javascript
// In browser console
getComputedStyle(document.documentElement).getPropertyValue('--primary')
```

### 3. Theme Testing
Toggle between themes to ensure consistency:
```javascript
// In browser console
document.documentElement.classList.toggle('dark')
```

## Resources

- [Clerk Appearance Documentation](https://clerk.com/docs/customization/appearance)
- [Design System Variables](../src/styles/globals.css)
- [Current Configuration](../src/features/auth/config/clerk-appearance.ts)

## Contributing

When modifying Clerk appearance:

1. **Never use hardcoded values**
2. **Test in both light and dark themes**
3. **Verify responsive behavior**
4. **Update this documentation if adding new patterns**
5. **Follow existing naming conventions**

Remember: Consistency with our design system is more important than pixel-perfect matching of external designs.