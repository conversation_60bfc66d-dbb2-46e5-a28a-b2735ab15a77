# JSON Schema to UI Component Field Mapping Documentation

## Overview
This document provides a comprehensive mapping of every field from the AI API JSON output (defined in `src/features/create-recipe/prompts/final-recipes.yaml`) to its corresponding usage in the frontend React components.

### **Primary Components Using Recipe Data:**
1. **`recipe-protocol-card.tsx`** - Detailed recipe view with collapsible sections
2. **`protocol-summary-card.tsx`** - Flip card overview of each recipe
3. **`safety-warnings.tsx`** - Safety warnings display 
4. **`final-recipes-display.tsx`** - Main container aggregating safety warnings

---

## Top-Level Structure Mapping

### Recipe Data Access Pattern
All recipe data is accessed through: `recipe` prop → `agentResult.finalOutput.data.recipe_protocol.*`

---

## Field-by-Field Mapping

### 1. TIME & IDENTIFICATION FIELDS

| JSON Field Path | UI Location | How Rendered | Example Value | Notes |
|---|---|---|---|---|
| `time_range_localized` | **Time Badge** - `recipe-protocol-card.tsx:36` | `<span className="font-bold">{recipe.time_range_localized}</span>` | `"6h às 9h"` | Large bold text in time badge section |
| `application_type_localized` | **Time Badge** - `recipe-protocol-card.tsx:37-39` | `<span className="inline-block bg-primary/10 text-primary text-xs font-semibold px-3 py-1 rounded-full">{recipe.application_type_localized}</span>` | `"Inalação e aplicação tópica suave na região abdominal"` | Small badge with colored background |
| `time_of_day_localized` | **Protocol Header** - `recipe-protocol-card.tsx:46`<br/>**Flip Card Front** - `protocol-summary-card.tsx:57` | `<div className="protocol-title">{recipe.time_of_day_localized}</div>`<br/>`<h3 className="text-2xl font-black">{recipe.time_of_day_localized}</h3>` | `"manhã"` | Main title in protocol header and flip card |
| `recipe_theme_localized` | **Protocol Header** - `recipe-protocol-card.tsx:47`<br/>**Flip Card Front** - `protocol-summary-card.tsx:64` | `<div className="protocol-subtitle">{recipe.recipe_theme_localized}</div>`<br/>`<div className="theme text-xl font-bold mb-6">{recipe.recipe_theme_localized}</div>` | `"Manhã Revigorante e Equilibrada"` | Subtitle in protocol header and flip card theme |

### 2. FORMULATION DATA

| JSON Field Path | UI Location | How Rendered | Example Value | Notes |
|---|---|---|---|---|
| `formulation.total_drops` | **Quick Info Grid** - `recipe-protocol-card.tsx:72-74` | `<div className="value text-lg font-bold text-foreground">{recipe.formulation.total_drops}</div>` | `15` | Large bold number with translated label |
| `formulation.dilution_percentage` | **Quick Info Grid** - `recipe-protocol-card.tsx:76-78` | `<div className="value text-lg font-bold text-foreground">{recipe.formulation.dilution_percentage}%</div>` | `1%` | Percentage display with % symbol |
| `formulation.bottle_size_ml` | **Quick Info Grid** - `recipe-protocol-card.tsx:80-82` | `<div className="value text-lg font-bold text-foreground">{recipe.formulation.bottle_size_ml}ml</div>` | `10ml` | Number with ml unit |
| `formulation.bottle_type_localized` | **Quick Info Grid** - `recipe-protocol-card.tsx:84-86` | `<div className="value text-lg font-bold text-foreground">{recipe.formulation.bottle_type_localized}</div>` | `"frasco de vidro escuro de 10ml"` | Text display of bottle type |

### 3. ESSENTIAL OILS INGREDIENTS

| JSON Field Path | UI Location | How Rendered | Example Value | Notes |
|---|---|---|---|---|
| `ingredients.essential_oils[]` | **Droplet Visualizer** - `recipe-protocol-card.tsx:52-62`<br/>**Flip Card Back** - `protocol-summary-card.tsx:103-109` | Visual droplets with colors based on oil index<br/>Ingredient list with drops count | Array of oil objects | Creates animated droplets for visualization and shows in flip card |
| `ingredients.essential_oils[].oil_id` | **Droplet Key** - `recipe-protocol-card.tsx:55`<br/>**List Key** - `protocol-summary-card.tsx:104` | `key={\`${oil.oil_id}-${dropIndex}\`}`<br/>`key={oil.oil_id}` | `"ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf"` | Used for React key prop |
| `ingredients.essential_oils[].drops` | **Droplet Count + Badge** - Multiple locations<br/>**Flip Card List** - `protocol-summary-card.tsx:106` | Droplet quantity and ingredient badge<br/>`<span className="font-mono text-sm text-muted-foreground">{oil.drops} drops</span>` | `5` | Controls droplet count and shows in badge/list |
| `ingredients.essential_oils[].name_localized` | **Ingredient Name** - `recipe-protocol-card.tsx:100`<br/>**Flip Card List** - `protocol-summary-card.tsx:105` | `<div className="name">{oil.name_localized}</div>`<br/>`<span className="text-primary">● {oil.name_localized}</span>` | `"Hortelã-pimenta"` | Primary ingredient name display |
| `ingredients.essential_oils[].scientific_name` | **Botanical Name** - `recipe-protocol-card.tsx:101` | `<div className="botanical">{oil.scientific_name}</div>` | `"Mentha Piperita"` | Scientific name in smaller text |

### 4. CARRIER OIL INFORMATION

| JSON Field Path | UI Location | How Rendered | Example Value | Notes |
|---|---|---|---|---|
| `ingredients.carrier_oil.recommended.name_localized` | **Recommended Carrier** - `recipe-protocol-card.tsx:113` | `<div className="name font-bold text-primary">{recipe.ingredients.carrier_oil.recommended.name_localized}</div>` | `"Óleo de massagem relaxante"` | Primary recommendation with blue styling |
| `ingredients.carrier_oil.recommended.properties_localized` | **Recommended Properties** - `recipe-protocol-card.tsx:114` | `<div className="properties text-xs text-primary/70">{recipe.ingredients.carrier_oil.recommended.properties_localized}</div>` | `"Óleo vegetal de base leve, ideal para massagens..."` | Small descriptive text |
| `ingredients.carrier_oil.alternative.name_localized` | **Alternative Carrier** - `recipe-protocol-card.tsx:119` | `<div className="name font-bold text-foreground">{recipe.ingredients.carrier_oil.alternative?.name_localized}</div>` | `"Óleo de coco"` | Alternative option |
| `ingredients.carrier_oil.alternative.properties_localized` | **Alternative Properties** - `recipe-protocol-card.tsx:120` | `<div className="properties text-xs text-muted-foreground">{recipe.ingredients.carrier_oil.alternative?.properties_localized}</div>` | `"Óleo vegetal de rápida absorção..."` | Small descriptive text |

### 5. USAGE INSTRUCTIONS (Collapsible Section)

| JSON Field Path | UI Location | How Rendered | Example Value | Notes |
|---|---|---|---|---|
| `usage_instructions_localized[]` | **Usage Section Content** - `recipe-protocol-card.tsx:147-155` | Dynamic grid of usage items | Array of instruction objects | Collapsible section content |
| `usage_instructions_localized[].method` | **Usage Method Title** - `recipe-protocol-card.tsx:149` | `<h5 className="font-semibold text-foreground">{instruction.method}</h5>` | `"Inalação direta"` | Bold method name |
| `usage_instructions_localized[].description` | **Usage Description** - `recipe-protocol-card.tsx:150` | `<p className="text-sm text-muted-foreground">{instruction.description}</p>` | `"Inale a mistura diretamente do frasco..."` | Descriptive text |
| `usage_instructions_localized[].frequency` | **Usage Frequency** - `recipe-protocol-card.tsx:151-153` | `<span className="frequency-tag inline-block mt-2 text-xs font-medium bg-primary/10 text-primary px-2 py-0.5 rounded-full">{instruction.frequency}</span>` | `"Manhã ao acordar"` | Small colored tag |

### 6. PREPARATION STEPS (Collapsible Section)

| JSON Field Path | UI Location | How Rendered | Example Value | Notes |
|---|---|---|---|---|
| `preparation_steps_localized[]` | **Preparation Checklist** - `recipe-protocol-card.tsx:178-182` | Dynamic checklist items | Array of step strings | Interactive checkboxes |
| `preparation_steps_localized[index]` | **Checklist Item** - `recipe-protocol-card.tsx:180` | `<span className="text-sm text-foreground">{step}</span>` | `"Adicione 5 gotas de hortelã-pimenta..."` | Text with checkbox |

### 7. SCIENTIFIC RATIONALES (Collapsible Section)

| JSON Field Path | UI Location | How Rendered | Example Value | Notes |
|---|---|---|---|---|
| `oil_rationales[]` | **Science Section Content** - `recipe-protocol-card.tsx:206-218` | Dynamic cards for each oil | Array of rationale objects | Explains science behind each oil |
| `oil_rationales[].name_localized` | **Oil Name** - `recipe-protocol-card.tsx:208` | `<h5 className="font-semibold text-foreground mb-2">{rationale.name_localized}</h5>` | `"Hortelã-pimenta"` | Bold oil name |
| `oil_rationales[].properties[]` | **Property Tags** - `recipe-protocol-card.tsx:210-214` | Dynamic property tag display | `["Antiespasmódico", "Anti-inflamatório"]` | Small colored tags |
| `oil_rationales[].rationale_localized` | **Scientific Explanation** - `recipe-protocol-card.tsx:216` | `<p className="text-sm text-muted-foreground">{rationale.rationale_localized}</p>` | `"Hortelã-pimenta é excelente para aliviar cólicas..."` | Detailed explanation |
| `ritual_suggestion_localized` | **Synergy Section** - `recipe-protocol-card.tsx:222` | `<p className="text-sm text-primary/90">{recipe.ritual_suggestion_localized}</p>` | `"Comece seu dia com uma respiração profunda..."` | Highlighted synergy text |

### 8. PROTOCOL SUMMARY CARD FIELDS (Flip Card Component)

| JSON Field Path | UI Location | How Rendered | Example Value | Notes |
|---|---|---|---|---|
| `holistic_benefit_localized` | **Flip Card Back** - `protocol-summary-card.tsx:97` | `<strong>Objective:</strong> {recipe.holistic_benefit_localized}` | `"Ao incorporar esta mistura na sua rotina matinal..."` | Benefits description with bold label |
| `preparation_summary_localized` | **Flip Card Back** - `protocol-summary-card.tsx:114` | `<strong>Quick Preparation:</strong> {recipe.preparation_summary_localized}` | `"Misture as gotas de óleos essenciais com o óleo transportador..."` | Brief preparation summary |
| `usage_summary_localized` | **Flip Card Back** - `protocol-summary-card.tsx:117` | `<strong>How to Use:</strong> {recipe.usage_summary_localized}` | `"Inale a mistura na manhã ao acordar ou aplique suavemente..."` | Brief usage summary |

### 9. SAFETY WARNINGS (Top-Level Component)

| JSON Field Path | UI Location | How Rendered | Example Value | Notes |
|---|---|---|---|---|
| `safety_warnings[]` | **Safety Component** - `safety-warnings.tsx:67-72`<br/>**Aggregated Display** - `final-recipes-display.tsx:793-799` | Dynamic accordion/details with color-coded warnings<br/>Flattened array from all recipes | Array of warning objects | Used by SafetyWarnings component |
| `safety_warnings[].type` | **Warning Styling** - `safety-warnings.tsx:18-25` | CSS classes based on type (`getWarningColorClass`) | `"precaução"`, `"alert"`, `"patch_test"` | Determines warning color scheme |
| `safety_warnings[].title_localized` | **Warning Title** - `safety-warnings.tsx:71` | `<summary className="cursor-pointer font-medium">{warning.title_localized}</summary>` | `"Evitar exposição ao sol"` | Collapsible section title |
| `safety_warnings[].warning_text_localized` | **Warning Content** - `safety-warnings.tsx:72` | `<div dangerouslySetInnerHTML={{ __html: warning.warning_text_localized }} />` | `"Evite exposição direta ao sol ou luz ultravioleta..."` | HTML content in collapsible section |

---

## Translation Keys Used

### Static UI Labels (Translated)
All static labels use the `t()` function with these translation keys:

#### Recipe Protocol Card Component
| Translation Key | UI Purpose | Component Location |
|---|---|---|
| `create-recipe:steps.final-recipes.recipeCard.totalDrops` | Quick info label | Line 72 |
| `create-recipe:steps.final-recipes.recipeCard.dilution` | Quick info label | Line 76 |
| `create-recipe:steps.final-recipes.recipeCard.size` | Quick info label | Line 80 |
| `create-recipe:steps.final-recipes.recipeCard.dispenser` | Quick info label | Line 84 |
| `create-recipe:steps.final-recipes.recipeCard.essentialOils` | Section header | Line 93 |
| `create-recipe:steps.final-recipes.recipeCard.drops` | Drop unit label | Line 97 |
| `create-recipe:steps.final-recipes.recipeCard.carrierOil` | Section header | Line 109 |
| `create-recipe:steps.final-recipes.recipeCard.recommended` | Carrier oil label | Line 112 |
| `create-recipe:steps.final-recipes.recipeCard.alternative` | Carrier oil label | Line 118 |
| `create-recipe:steps.final-recipes.recipeCard.sections.usage.title` | Collapsible section title | Line 135 |
| `create-recipe:steps.final-recipes.recipeCard.sections.usage.subtitle` | Collapsible section subtitle | Line 136 |
| `create-recipe:steps.final-recipes.recipeCard.sections.preparation.title` | Collapsible section title | Line 168 |
| `create-recipe:steps.final-recipes.recipeCard.sections.preparation.subtitle` | Collapsible section subtitle | Line 169 |
| `create-recipe:steps.final-recipes.recipeCard.sections.science.title` | Collapsible section title | Line 197 |
| `create-recipe:steps.final-recipes.recipeCard.sections.science.subtitle` | Collapsible section subtitle | Line 198 |
| `create-recipe:steps.final-recipes.recipeCard.sections.science.rationale` | Synergy section title | Line 221 |

#### Protocol Summary Card Component  
| Translation Key | UI Purpose | Component Location |
|---|---|---|
| `create-recipe:steps.final-recipes.loading` | Loading state text | Line 35 |
| `create-recipe:steps.final-recipes.protocolSummary.synergyFor` | Front card label | Line 63 |
| `create-recipe:steps.final-recipes.overview.protocolSummary.viewDetails` | Flip button text | Line 70 |
| `create-recipe:steps.final-recipes.overview.protocolSummary.clickToView` | Instructions text | Line 78 |
| `create-recipe:steps.final-recipes.overview.protocolSummary.close` | Close button aria-label | Line 87 |
| `create-recipe:steps.final-recipes.protocolSummary.objective` | Back card section label | Line 97 |
| `create-recipe:steps.final-recipes.protocolSummary.drops` | Drops unit label | Line 106 |
| `create-recipe:steps.final-recipes.protocolSummary.quickPreparation` | Preparation section label | Line 114 |
| `create-recipe:steps.final-recipes.protocolSummary.howToUse` | Usage section label | Line 117 |
| `create-recipe:steps.final-recipes.overview.protocolSummary.viewRecipe` | View recipe link text | Line 123 |

---

## Unused Fields in Current UI

### Schema Fields Not Currently Displayed

| JSON Field Path | Schema Type | Reason Not Used | Potential UI Location |
|---|---|---|---|
| `description_localized` | string | No general description area in UI | Could be added as intro text on flip card or protocol card |
| `synergy_rationale_localized` | string | Not used (using ritual_suggestion instead) | Could replace or complement ritual section |
| `therapeutic_properties_targeted[]` | array | No dedicated properties section | Could be added as benefit tags on flip card |
| `usage_instructions_localized[].method_code` | string | Only method name displayed | Could be used for filtering/categorization |

### Fields Used in New Components (Previously Unused)
✅ **`holistic_benefit_localized`** - Now used in protocol summary card back  
✅ **`preparation_summary_localized`** - Now used in protocol summary card back  
✅ **`usage_summary_localized`** - Now used in protocol summary card back  
✅ **`safety_warnings[]`** - Now used in safety warnings component  

### Metadata Fields Not Displayed
- `meta.*` - All metadata fields are not shown in UI components
- `echo.*` - Echo data not displayed (debug/tracking only)

---

## Special Behaviors & Transformations

### 1. Droplet Visualization Logic
- **Color Assignment**: Uses `getDropletColor(oilIndex)` - colors assigned by array index, not oil properties
- **Animation**: Each droplet gets unique `animationDelay` based on oil position and droplet position
- **Height Randomization**: Each droplet height varies with `Math.random() * 8`

### 2. Data Safety Handling
- All carrier oil alternative fields use optional chaining (`?.`) for null safety
- Component has fallback null state with loading UI

### 3. Key Prop Strategy
- Essential oils use `oil.oil_id` as React key
- Usage instructions use array `index` as key
- Preparation steps use array `index` as key
- Oil rationales use array `index` as key

---

## Recommendations for Prompt/Schema Improvements

### 1. Content Length Guidelines
- **`recipe_theme_localized`**: Keep under 40 characters (displays as subtitle)
- **`time_of_day_localized`**: Keep under 20 characters (displays as main title)
- **`application_type_localized`**: Keep under 60 characters (small badge display)
- **`usage_instructions_localized[].method`**: Keep under 30 characters (bold title)
- **`usage_instructions_localized[].frequency`**: Keep under 25 characters (small tag)

### 2. Tone & Style Guidelines
- **Scientific explanations** (`oil_rationales[].rationale_localized`): Educational but accessible tone
- **Instructions** (`preparation_steps_localized[]`): Clear, actionable steps
- **Ritual suggestion** (`ritual_suggestion_localized`): Warm, encouraging, mindful tone

### 3. Structure Recommendations
- **Usage instructions**: Limit to 2-4 methods to avoid overcrowding
- **Preparation steps**: Keep to 3-5 steps maximum for clarity
- **Oil selection**: Current UI works best with 3-5 oils (affects droplet visualization)
- **Property tags**: Limit to 3-4 properties per oil for clean display

---

## Component Dependencies

### Component Hierarchy
```
final-recipes-display.tsx (Main Container)
├── protocol-summary-card.tsx (Flip Cards)
├── recipe-protocol-card.tsx (Detailed View)  
└── safety-warnings.tsx (Safety Accordion)
```

### External Dependencies
- `useI18n` hook for translations (all components)
- `getDropletColor` utility for droplet colors (`recipe-protocol-card.tsx`)
- `getTimeSlotConfig` utility for flip card styling (`protocol-summary-card.tsx`)
- `RecipeTimeSlot` and `FinalRecipeProtocol` type definitions
- Translation files under `src/lib/i18n/messages/`

### Styling Dependencies
- Tailwind CSS classes for all styling
- CSS variables for theme colors (primary, secondary, muted, etc.)
- Custom CSS classes: 
  - `collapsible-strip`, `droplet`, `protocol-card` (`recipe-protocol-card.tsx`)
  - `flip-card`, `flip-card-inner`, `flip-card-front`, `flip-card-back` (`protocol-summary-card.tsx`)
  - Warning color classes in `safety-warnings.tsx`

### Data Flow
1. **`final-recipes-display.tsx`** aggregates safety warnings from all recipes
2. **`protocol-summary-card.tsx`** shows recipe overview with flip interaction  
3. **`recipe-protocol-card.tsx`** displays full recipe details
4. **`safety-warnings.tsx`** renders collapsible safety information
