# Clerk User Data Management Guide

## Overview

This document explains how to properly save and manage user data in Clerk, addressing the differences between client-side and server-side approaches, and providing a centralized solution for consistent data management.

## The Problem We Solved

### Original Issue
- **Onboarding flow**: Server-side data saving wasn't reflecting on the client
- **Profile page**: Client-side data saving worked but wasn't consistent
- **Data synchronization**: Client and server weren't properly synchronized after updates

### Root Cause
The main issue was the difference between client-side and server-side data updates:

1. **Server-side updates** (using `clerkClient()`) update the database immediately
2. **Client-side user object** doesn't automatically refresh after server updates
3. **Session tokens** need to be refreshed to include the latest metadata

## How We Fixed It

### 1. Server-Side Updates (Onboarding)
```typescript
// ❌ BEFORE: Data saved but client didn't see it
await clerkClient().users.updateUserMetadata(userId, { ... });
// Client still had old data

// ✅ AFTER: Added proper client refresh
await clerkClient().users.updateUserMetadata(userId, { ... });
// Then on client:
await user.reload(); // Force refresh user data
await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for propagation
```

### 2. Client-Side Updates (Profile)
```typescript
// ✅ Client-side updates work immediately
await user.update({
  unsafeMetadata: { ...user.unsafeMetadata, language: 'pt' }
});
// No additional refresh needed
```

## Centralized Solution: UserDataService

We created a unified service to handle all user data operations consistently.

### Installation

```bash
# Copy the UserDataService utility
cp src/lib/services/user-data.service.ts your-project/
```

### Usage Examples

#### 1. Server-Side Update (Server Actions, API Routes)

```typescript
'use server';
import { UserDataService } from '@/lib/services/user-data.service';
import { auth } from '@clerk/nextjs/server';

export async function updateUserOnboarding(data: OnboardingData) {
  const { userId } = await auth();
  
  // Server-side update
  const result = await UserDataService.updateUserDataServer(userId, {
    firstName: data.firstName,
    lastName: data.lastName,
    language: data.language,
    onboardingComplete: true,
    onboardingCompletedAt: new Date().toISOString(),
  }, {
    updateBasicInfo: true, // Also update firstName/lastName
  });

  return result;
}
```

#### 2. Client-Side Refresh (After Server Updates)

```typescript
'use client';
import { UserDataService } from '@/lib/services/user-data.service';
import { useUser } from '@clerk/nextjs';

export function OnboardingWizard() {
  const { user } = useUser();

  const completeOnboarding = async () => {
    // Call server action
    const result = await updateUserOnboarding(data);
    
    if (result.success && user) {
      // CRITICAL: Refresh client data after server update
      await UserDataService.refreshUserDataClient(user);
      
      // Now user.unsafeMetadata has the updated data
      console.log('Updated metadata:', user.unsafeMetadata);
    }
  };
}
```

#### 3. Direct Client-Side Update (Profile Forms)

```typescript
'use client';
import { UserDataService } from '@/lib/services/user-data.service';
import { useUser } from '@clerk/nextjs';

export function ProfileForm() {
  const { user } = useUser();

  const updateProfile = async (updates: { language: string; bio: string }) => {
    if (!user) return;

    const result = await UserDataService.updateUserDataClient(user, updates, {
      forceReload: true, // Ensure data is fresh
    });

    if (result.success) {
      console.log('Profile updated successfully');
    }
  };
}
```

## Best Practices

### When to Use Each Approach

| Scenario | Approach | Reason |
|----------|----------|---------|
| Onboarding flow | Server-side + Client refresh | Server validation, security |
| Profile forms | Client-side | Immediate feedback, simpler |
| Admin operations | Server-side | Security, validation |
| Bulk operations | Server-side | Performance |

### Data Synchronization Rules

1. **After server-side updates**: Always call `UserDataService.refreshUserDataClient()`
2. **After client-side updates**: No additional refresh needed
3. **Critical updates**: Use `forceReload: true` option
4. **Wait for propagation**: Use 1000ms delay for server updates

### Metadata Structure

```typescript
// Recommended user metadata structure
{
  // User preferences
  language: 'en' | 'pt' | 'es',
  theme: 'light' | 'dark',
  
  // Profile data
  bio: string,
  
  // Onboarding
  onboardingComplete: boolean,
  onboardingCompletedAt: string,
  onboardingSteps: string[],
  
  // System
  updatedAt: string,
}
```

## Common Pitfalls to Avoid

### ❌ Don't Do This

```typescript
// Server action without client refresh
export async function updateUser(data) {
  await clerkClient().users.updateUserMetadata(userId, data);
  // ❌ Client won't see the changes
}

// Mixed approaches without coordination
await user.update({ unsafeMetadata: { language: 'pt' } }); // Client
await updateUserOnServer({ bio: 'New bio' }); // Server
// ❌ Data might be inconsistent
```

### ✅ Do This Instead

```typescript
// Server action with proper client refresh pattern
export async function updateUser(data) {
  const result = await UserDataService.updateUserDataServer(userId, data);
  return result; // Client will handle refresh
}

// Client handles the full flow
const handleUpdate = async () => {
  const result = await updateUser(data);
  if (result.success && user) {
    await UserDataService.refreshUserDataClient(user);
  }
};
```

## Migration Guide

### Step 1: Install the Service
Copy `src/lib/services/user-data.service.ts` to your project.

### Step 2: Update Server Actions
```typescript
// Before
await clerkClient().users.updateUserMetadata(userId, data);

// After
await UserDataService.updateUserDataServer(userId, data);
```

### Step 3: Update Client Components
```typescript
// Before
await user.update({ unsafeMetadata: { ...user.unsafeMetadata, ...data } });

// After
await UserDataService.updateUserDataClient(user, data);
```

### Step 4: Add Client Refresh After Server Updates
```typescript
// After any server action call:
if (result.success && user) {
  await UserDataService.refreshUserDataClient(user);
}
```

## Debugging

### Enable Debug Logs
Set `NEXT_PUBLIC_DEBUG=true` in your `.env.local` to see detailed logs.

### Common Debug Patterns
```typescript
// Check user data before/after updates
console.log('Before:', user.unsafeMetadata);
await UserDataService.updateUserDataClient(user, data);
console.log('After:', user.unsafeMetadata);

// Verify server updates reached client
const beforeReload = user.unsafeMetadata;
await UserDataService.refreshUserDataClient(user);
const afterReload = user.unsafeMetadata;
console.log('Data changed:', JSON.stringify(beforeReload) !== JSON.stringify(afterReload));
```

## Conclusion

The key to successful Clerk user data management is understanding the client/server synchronization requirements:

1. **Server updates** require **client refresh**
2. **Client updates** work immediately
3. **Always use the centralized service** for consistency
4. **Test the full flow** including data refresh

This approach ensures reliable, consistent user data management across your entire application.
