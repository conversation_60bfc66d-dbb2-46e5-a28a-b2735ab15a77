#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Find SAFE orphan files - files that are clearly unused and safe to delete
 * This is a more conservative version that focuses on obvious dead code
 */

class SafeOrphanFinder {
  constructor() {
    this.safeOrphans = [];
  }

  // Check if a file is clearly safe to delete
  isSafeOrphan(filePath) {
    const fileName = path.basename(filePath);
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Patterns that indicate a file is likely safe to delete
    const safePatterns = [
      // Files with "legacy", "old", "backup", "copy" in name
      /legacy|old|backup|copy|temp|test-/i,
      
      // Files ending with numbers (often duplicates)
      /\d+\.(tsx?|jsx?)$/,
      
      // Files with "unused" or "deprecated" comments
      content.includes('// unused') || content.includes('// deprecated') || 
      content.includes('/* unused') || content.includes('/* deprecated'),
      
      // Empty or near-empty files (less than 50 characters)
      content.trim().length < 50,
      
      // Files that only export empty objects/arrays
      /^(export\s+)?(const|let|var)\s+\w+\s*=\s*(\{\}|\[\])\s*;?\s*$/m.test(content.trim()),
    ];
    
    return safePatterns.some(pattern => {
      if (pattern instanceof RegExp) {
        return pattern.test(fileName) || pattern.test(content);
      }
      return pattern;
    });
  }

  // Get all TypeScript/JavaScript files
  getAllFiles(dir, files = []) {
    if (!fs.existsSync(dir)) return files;
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!['node_modules', '.next', '.git', 'dist', 'build'].includes(item)) {
          this.getAllFiles(fullPath, files);
        }
      } else if (/\.(tsx?|jsx?)$/.test(item)) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  // Check if file is imported anywhere
  isFileImported(targetFile, allFiles) {
    const targetName = path.basename(targetFile, path.extname(targetFile));
    const targetDir = path.dirname(targetFile);
    
    for (const file of allFiles) {
      if (file === targetFile) continue;
      
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        // Check for various import patterns
        const importPatterns = [
          new RegExp(`from\\s+['"\`][^'"\`]*${targetName}['"\`]`, 'g'),
          new RegExp(`import\\s*\\(\\s*['"\`][^'"\`]*${targetName}['"\`]\\s*\\)`, 'g'),
          new RegExp(`require\\s*\\(\\s*['"\`][^'"\`]*${targetName}['"\`]\\s*\\)`, 'g'),
        ];
        
        if (importPatterns.some(pattern => pattern.test(content))) {
          return true;
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }
    
    return false;
  }

  // Find safe orphan files
  findSafeOrphans() {
    console.log('🔍 Scanning for SAFE orphan files...\n');
    
    const allFiles = this.getAllFiles('src');
    console.log(`📁 Found ${allFiles.size} files to analyze`);
    
    const potentialOrphans = allFiles.filter(file => {
      // Skip Next.js special files and other important files
      if (/\/(page|layout|loading|error|not-found|route|middleware)\.(tsx?|jsx?)$/.test(file)) {
        return false;
      }
      
      // Check if it's a safe orphan candidate
      return this.isSafeOrphan(file);
    });
    
    console.log(`🎯 Found ${potentialOrphans.length} potential safe orphans`);
    
    // Double-check that they're not imported
    for (const file of potentialOrphans) {
      if (!this.isFileImported(file, allFiles)) {
        this.safeOrphans.push(file);
      }
    }
    
    return this.safeOrphans;
  }

  // Generate report
  generateReport() {
    const safeOrphans = this.findSafeOrphans();
    
    console.log('\n' + '='.repeat(60));
    console.log('🗑️  SAFE ORPHAN FILES REPORT');
    console.log('='.repeat(60));
    
    if (safeOrphans.length === 0) {
      console.log('✅ No obvious safe orphan files found!');
      console.log('   Your codebase appears to be well-maintained.');
      return;
    }
    
    console.log(`\n❌ Found ${safeOrphans.length} SAFE orphan files:\n`);
    
    safeOrphans.forEach(file => {
      console.log(`🗑️  ${file}`);
      
      // Show why it's considered safe
      const fileName = path.basename(file);
      const content = fs.readFileSync(file, 'utf8');
      
      if (/legacy|old|backup|copy|temp/i.test(fileName)) {
        console.log(`   └─ Reason: Filename suggests legacy/backup file`);
      } else if (/\d+\.(tsx?|jsx?)$/.test(fileName)) {
        console.log(`   └─ Reason: Numbered file (likely duplicate)`);
      } else if (content.includes('// unused') || content.includes('/* unused')) {
        console.log(`   └─ Reason: Marked as unused in comments`);
      } else if (content.trim().length < 50) {
        console.log(`   └─ Reason: Nearly empty file (${content.trim().length} chars)`);
      }
      console.log('');
    });
    
    // Generate safe cleanup script
    console.log('🧹 SAFE cleanup script:');
    console.log('');
    console.log('#!/bin/bash');
    console.log('# Safe orphan file cleanup');
    console.log('');
    safeOrphans.forEach(file => {
      console.log(`rm "${file}"`);
    });
    
    console.log('\n✅ These files are considered SAFE to delete because they:');
    console.log('   - Have obvious indicators (legacy, copy, numbered, etc.)');
    console.log('   - Are not imported by any other files');
    console.log('   - Are marked as unused or are nearly empty');
    
    return safeOrphans;
  }
}

// Run the analysis
if (require.main === module) {
  const finder = new SafeOrphanFinder();
  finder.generateReport();
}

module.exports = SafeOrphanFinder;