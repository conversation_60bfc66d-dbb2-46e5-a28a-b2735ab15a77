#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Find orphan files in the codebase
 * Files that are not imported anywhere and are just cluttering the codebase
 */

// Configuration
const CONFIG = {
  // Directories to scan for files
  scanDirs: ['src'],
  
  // File extensions to check
  extensions: ['.tsx', '.ts', '.js', '.jsx'],
  
  // Files/patterns to always exclude (these are entry points or special files)
  excludePatterns: [
    // Next.js special files
    /page\.(tsx?|jsx?)$/,
    /layout\.(tsx?|jsx?)$/,
    /loading\.(tsx?|jsx?)$/,
    /error\.(tsx?|jsx?)$/,
    /not-found\.(tsx?|jsx?)$/,
    /global-error\.(tsx?|jsx?)$/,
    /route\.(tsx?|jsx?)$/,
    /middleware\.(tsx?|jsx?)$/,
    /instrumentation\.(tsx?|jsx?)$/,
    
    // Config files
    /\.config\.(tsx?|jsx?)$/,
    /tailwind\.config/,
    /next\.config/,
    
    // Entry points
    /^app\//,
    /^pages\//,
    
    // Test files
    /\.(test|spec)\.(tsx?|jsx?)$/,
    /__tests__/,
    
    // Type definition files
    /\.d\.ts$/,
    
    // Specific files that might not be directly imported
    /globals\.css$/,
    /env\./,
  ],
  
  // Directories to exclude completely
  excludeDirs: [
    'node_modules',
    '.next',
    '.git',
    'dist',
    'build',
    'coverage',
    '__tests__',
    '.kiro'
  ]
};

class OrphanFileFinder {
  constructor() {
    this.allFiles = new Set();
    this.importedFiles = new Set();
    this.orphanFiles = [];
  }

  // Get all files in the specified directories
  getAllFiles(dir, files = []) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!CONFIG.excludeDirs.includes(item)) {
          this.getAllFiles(fullPath, files);
        }
      } else if (CONFIG.extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  // Check if file should be excluded based on patterns
  shouldExclude(filePath) {
    return CONFIG.excludePatterns.some(pattern => {
      if (pattern instanceof RegExp) {
        return pattern.test(filePath);
      }
      return filePath.includes(pattern);
    });
  }

  // Extract imports from a file
  extractImports(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const imports = new Set();
      
      // Match various import patterns
      const importPatterns = [
        // ES6 imports: import ... from '...'
        /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"`]([^'"`]+)['"`]/g,
        // Dynamic imports: import('...')
        /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
        // Require: require('...')
        /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
        // Next.js dynamic: dynamic(() => import('...'))
        /dynamic\s*\(\s*\(\s*\)\s*=>\s*import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)\s*\)/g,
      ];

      for (const pattern of importPatterns) {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          const importPath = match[1];
          
          // Skip external packages (don't start with . or /)
          if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
            continue;
          }
          
          // Resolve relative imports
          const resolvedPath = this.resolveImportPath(filePath, importPath);
          if (resolvedPath) {
            imports.add(resolvedPath);
          }
        }
      }
      
      return imports;
    } catch (error) {
      console.warn(`Warning: Could not read file ${filePath}: ${error.message}`);
      return new Set();
    }
  }

  // Resolve import path to actual file path
  resolveImportPath(fromFile, importPath) {
    const fromDir = path.dirname(fromFile);
    let resolvedPath;
    
    if (importPath.startsWith('.')) {
      // Relative import
      resolvedPath = path.resolve(fromDir, importPath);
    } else if (importPath.startsWith('/')) {
      // Absolute import from project root
      resolvedPath = path.resolve(process.cwd(), importPath.slice(1));
    } else {
      return null; // External package
    }
    
    // Try different extensions if file doesn't exist
    if (fs.existsSync(resolvedPath)) {
      return resolvedPath;
    }
    
    for (const ext of CONFIG.extensions) {
      const withExt = resolvedPath + ext;
      if (fs.existsSync(withExt)) {
        return withExt;
      }
    }
    
    // Try index files
    for (const ext of CONFIG.extensions) {
      const indexFile = path.join(resolvedPath, `index${ext}`);
      if (fs.existsSync(indexFile)) {
        return indexFile;
      }
    }
    
    return null;
  }

  // Main analysis function
  analyze() {
    console.log('🔍 Scanning for orphan files...\n');
    
    // Get all files
    for (const dir of CONFIG.scanDirs) {
      if (fs.existsSync(dir)) {
        const files = this.getAllFiles(dir);
        files.forEach(file => this.allFiles.add(file));
      }
    }
    
    console.log(`📁 Found ${this.allFiles.size} files to analyze`);
    
    // Extract all imports
    for (const file of this.allFiles) {
      const imports = this.extractImports(file);
      imports.forEach(imp => this.importedFiles.add(imp));
    }
    
    console.log(`📦 Found ${this.importedFiles.size} imported files`);
    
    // Find orphan files
    for (const file of this.allFiles) {
      if (!this.importedFiles.has(file) && !this.shouldExclude(file)) {
        this.orphanFiles.push(file);
      }
    }
    
    return this.orphanFiles;
  }

  // Generate report
  generateReport() {
    const orphans = this.analyze();
    
    console.log('\n' + '='.repeat(60));
    console.log('🗑️  ORPHAN FILES REPORT');
    console.log('='.repeat(60));
    
    if (orphans.length === 0) {
      console.log('✅ No orphan files found! Your codebase is clean.');
      return;
    }
    
    console.log(`\n❌ Found ${orphans.length} orphan files:\n`);
    
    // Group by directory for better readability
    const groupedOrphans = {};
    orphans.forEach(file => {
      const dir = path.dirname(file);
      if (!groupedOrphans[dir]) {
        groupedOrphans[dir] = [];
      }
      groupedOrphans[dir].push(path.basename(file));
    });
    
    // Display grouped results
    Object.keys(groupedOrphans).sort().forEach(dir => {
      console.log(`📂 ${dir}/`);
      groupedOrphans[dir].sort().forEach(file => {
        console.log(`   ├── ${file}`);
      });
      console.log('');
    });
    
    // Generate cleanup commands
    console.log('🧹 To remove these files, run:');
    console.log('');
    orphans.forEach(file => {
      console.log(`rm "${file}"`);
    });
    
    console.log('\n⚠️  WARNING: Review each file before deletion!');
    console.log('   Some files might be:');
    console.log('   - Entry points not detected by this script');
    console.log('   - Used by external tools or configs');
    console.log('   - Imported dynamically in ways not detected');
    
    return orphans;
  }
}

// Run the analysis
if (require.main === module) {
  const finder = new OrphanFileFinder();
  finder.generateReport();
}

module.exports = OrphanFileFinder;