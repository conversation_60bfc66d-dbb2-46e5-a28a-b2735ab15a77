#!/bin/bash
# SSafe orphan file cleanup - Generated by find-safe-orphans.js

echo "🧹 Cleaning up safe orphan files..."

# Files with obvious indicators (legacy, copy, backup, etc.)
rm "src/app/[locale]/(dashboard)/dashboard/chat/page-older-version-legacy.tsx"
rm "src/app/api/ai/streaming/route copy.ts"

# Layout files that appear to be old versions
rm "src/app/[locale]/layout-new.tsx"

# Example/debug files
rm "src/components/examples/client-component-with-translations.tsx"
rm "src/features/auth/components/debug/clerk-user-debugger.tsx"

# Nearly empty index files
rm "src/features/auth/schemas/index.ts"
rm "src/features/create-recipe/utils/index.ts"
rm "src/features/homepage/index.ts"
rm "src/features/onboarding/components/index.ts"
rm "src/lib/i18n/index.ts"

# Test files (if not needed)
rm "src/features/create-recipe/utils/__tests__/ai-enhanced-data-validation.test.ts"
rm "src/features/create-recipe/utils/__tests__/data-flow-verification.test.ts"
rm "src/features/create-recipe/utils/__tests__/final-recipes-template.test.ts"
rm "src/features/create-recipe/utils/__tests__/oil-data-enrichment.test.ts"
rm "src/features/create-recipe/utils/__tests__/prompt-manager-debug.test.ts"
rm "src/features/create-recipe/utils/__tests__/template-data-configurations.test.ts"

# Unused components and utilities
rm "src/features/create-recipe/components/generic-step-selector.tsx"
rm "src/features/create-recipe/utils/local-storage.ts"
rm "src/features/dashboard/components/user-greeting.tsx"
rm "src/features/dashboard/profile/profile-form-simple.tsx"
rm "src/features/dashboard/profile/profile-view.tsx"
rm "src/features/onboarding/components/simple-select.tsx"
rm "src/hooks/use-render-performance-monitor.tsx"
rm "src/stores/auth.store.ts"

# Setup/config files that might not be needed
rm "src/setupTests.ts"
rm "src/app/global-error.tsx"

echo "✅ Cleanup complete! Removed safe orphan files."
echo "📊 You can run 'git status' to see what was removed."